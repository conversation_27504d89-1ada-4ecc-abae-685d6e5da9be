<?php
declare(strict_types=1);

namespace App\Service;

use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\User;
use Doctrine\Persistence\Proxy;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Workflow\Registry;

class WorkflowSimulator
{
    private Registry $registry;
    private object $userRepository;

    public function __construct(Registry $registry, ManagerRegistry $doctrine)
    {
        $this->registry = $registry;
        $this->userRepository = $doctrine->getRepository(User::class);
    }

    public function simulateWorkflowList(array $documents){
        $paniers = ['Achat_Hts', "QProd", "methode_Labo", "Methode_assemblage", "Achat_RoHs_REACH"];
        $simulated = [];
        foreach ($documents as $document){
            $doc_paniers = ['oldPlaces' => [], 'currentSteps' => [], 'visitedPlaces' => []];
            $visas = $document->getVisas();
            $past_place = [];
            foreach ($visas as $visa){
                $past_place[str_replace('visa_', '', $visa->getName())] = 1;
            }
            $current_place = $document->getCurrentSteps();
            $futur_place = array_diff_key($this->simulateWorkflow($document), $current_place);
            $current_place = array_diff_key($current_place, $past_place);
            if (array_key_exists('BE_0', $past_place) && array_key_exists('BE_1', $past_place) && array_key_exists('BE', $past_place)){
                unset($past_place['BE_0']);
                unset($past_place['BE_1']);
            }
            if (array_key_exists('BE_0', $futur_place) && array_key_exists('BE_1', $futur_place) && array_key_exists('BE', $futur_place)){
                unset($futur_place['BE_0']);
                unset($futur_place['BE_1']);
            }
            foreach ($paniers as $panier){
                if (array_key_exists($panier, $past_place)){
                    $doc_paniers['oldPlaces'][$panier][] = $document;
                    unset($past_place[$panier]);
                }
                if (array_key_exists($panier, $current_place)){
                    $doc_paniers['currentSteps'][$panier][] = $document;
                    unset($current_place[$panier]);
                }
                if (array_key_exists($panier, $futur_place)){
                    $doc_paniers['visitedPlaces'][$panier][] = $document;
                    unset($futur_place[$panier]);
                }
            }

            $doc_paniers['visitedPlaces'] = array_diff_key($doc_paniers['visitedPlaces'], $doc_paniers['currentSteps']);
            $doc_paniers['currentSteps'] = array_diff_key($doc_paniers['currentSteps'], $doc_paniers['oldPlaces']);

            $simulated[] = [
                'document' => $document,
                'oldPlaces' => $past_place,
                'currentSteps' => $current_place,
                'visitedPlaces' => $futur_place,
                'paniers' => $doc_paniers
            ];
        }

        return $simulated;
    }

    public function simulateWorkflow(Document $document)
    {
        $workflow = $this->registry->get($document, 'document_workflow');        
        $currentPlace = $workflow->getMarking($document)->getPlaces();

        $i=0;
        $change = true;
        while ($change){
            $i++;
            $newPlace = $currentPlace;
            $change = false;
            foreach ($currentPlace as $place=>$val){
                if(in_array($place, ['Assembly', 'Machining', 'Molding'], true)){
                    $place = "prod";
                }
                $this->addFakeVisa($document,$place);
            }
            $transitions = $workflow->getEnabledTransitions($document);
            foreach ($transitions as $transition){
                $newPlace[$transition->getTos()[0]] = 1;
            }
            if(count($newPlace) > count($currentPlace)){
                $change = true;
                $currentPlace = $newPlace;
                $document->setCurrentSteps($currentPlace);
            }
        }

        return $currentPlace;
    }

    public function addFakeVisa(Document $document, string $place){
        $visa = new Visa();
        $visa->setReleasedDrawing($document);
        $visa->setName('visa_'.$place);
        $visa->setStatus('valid');
        $visa->setValidator($this->userRepository->find(1));
        $document->addVisa($visa);
    }

}