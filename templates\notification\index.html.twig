{% extends 'base.html.twig' %}

{% block title %}Notifications{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .notification-header {
        background-color: #f0f4f8;
        border-radius: 4px;
        padding: 15px 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #0275d8;
    }

    .notification-card {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }

    .notification-list {
        max-height: 600px;
        overflow-y: auto;
    }

    .notification-item {
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        transition: background-color 0.2s;
    }

    .notification-item:hover {
        background-color: #f8f9fa;
    }

    .notification-item:last-child {
        border-bottom: none;
    }

    .notification-item.unread {
        background-color: #f0f7ff;
        border-left: 4px solid #0275d8;
    }

    .notification-item.unread:hover {
        background-color: #e6f2ff;
    }

    .notification-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        width: 30px;
        text-align: center;
    }

    .notification-icon.danger {
        color: #dc3545;
    }

    .notification-icon.warning {
        color: #ffc107;
    }

    .notification-icon.success {
        color: #28a745;
    }

    .notification-icon.info {
        color: #0dcaf0;
    }

    .notification-title {
        font-weight: 600;
        margin-bottom: 5px;
        color: #212529;
    }

    .notification-message {
        color: #6c757d;
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    .notification-time {
        font-size: 0.8rem;
        color: #adb5bd;
    }

    .notification-actions {
        display: flex;
        align-items: center;
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 0.7rem;
    }

    .notification-filters {
        margin-bottom: 20px;
    }

    .filter-btn {
        margin-right: 8px;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .filter-btn.active {
        background-color: #0275d8;
        color: white;
        border-color: #0275d8;
    }

    .btn-outline-primary, .btn-outline-secondary, .btn-outline-danger,
    .btn-outline-warning, .btn-outline-success, .btn-outline-info {
        border-radius: 4px;
        font-weight: 500;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 3px;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 15px;
        color: #adb5bd;
    }

    .empty-state p {
        font-size: 1rem;
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="notification-header d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-bell text-primary me-2"></i>
                Notifications
                {% if unread_count > 0 %}
                    <span class="badge bg-danger ms-2">{{ unread_count }}</span>
                {% endif %}
            </h1>
            <p class="text-muted mb-0">Gérez vos notifications et alertes</p>
        </div>
        <div>
            <button id="markAllAsRead" class="btn btn-outline-primary me-2" {% if unread_count == 0 %}disabled{% endif %}>
                <i class="fas fa-check-double"></i> Tout marquer comme lu
            </button>
            <a href="{{ path('app_dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-tachometer-alt"></i> Tableau de bord
            </a>
        </div>
    </div>

    <div class="notification-filters">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary filter-btn active" data-filter="all">Toutes</button>
            <button type="button" class="btn btn-outline-secondary filter-btn" data-filter="unread">Non lues</button>
            <button type="button" class="btn btn-outline-danger filter-btn" data-filter="danger">Urgentes</button>
            <button type="button" class="btn btn-outline-warning filter-btn" data-filter="warning">Avertissements</button>
            <button type="button" class="btn btn-outline-success filter-btn" data-filter="success">Succès</button>
            <button type="button" class="btn btn-outline-info filter-btn" data-filter="info">Informations</button>
        </div>
    </div>

    <div class="notification-card">
        <div class="notification-list">
            {% if notifications|length > 0 %}
                {% for notification in notifications %}
                    <div class="notification-item {% if not notification.isRead %}unread{% endif %}"
                         data-id="{{ notification.id }}"
                         data-severity="{{ notification.severity }}"
                         data-read="{{ notification.isRead ? 'true' : 'false' }}">
                        <div class="d-flex">
                            <div class="notification-icon {{ notification.severity }}">
                                {% if notification.severity == 'danger' %}
                                    <i class="fas fa-exclamation-circle"></i>
                                {% elseif notification.severity == 'warning' %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% elseif notification.severity == 'success' %}
                                    <i class="fas fa-check-circle"></i>
                                {% else %}
                                    <i class="fas fa-info-circle"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <div class="notification-title">{{ notification.title }}</div>
                                <div class="notification-message">{{ notification.message }}</div>
                                <div class="notification-time">{{ notification.createdAt|date('d/m/Y H:i') }}</div>
                            </div>
                            <div class="notification-actions">
                                {% if notification.link %}
                                    <a href="{{ notification.link }}" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="fas fa-eye"></i> Voir
                                    </a>
                                {% endif %}
                                {% if not notification.isRead %}
                                    <button class="btn btn-sm btn-outline-secondary mark-as-read" data-id="{{ notification.id }}">
                                        <i class="fas fa-check"></i> Marquer comme lu
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash text-muted fa-3x mb-3"></i>
                    <p>Vous n'avez aucune notification</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Marquer une notification comme lue
        const markAsReadButtons = document.querySelectorAll('.mark-as-read');
        markAsReadButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.dataset.id;

                fetch(`{{ path('app_notifications_mark_as_read', {'id': 'ID'}) }}`.replace('ID', id), {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Mettre à jour l'interface
                        const notificationItem = this.closest('.notification-item');
                        notificationItem.classList.remove('unread');
                        notificationItem.dataset.read = 'true';
                        this.remove();

                        // Mettre à jour le compteur
                        const unreadCount = document.querySelectorAll('.notification-item.unread').length;
                        const badge = document.querySelector('.notification-header .badge');

                        if (badge) {
                            if (unreadCount > 0) {
                                badge.textContent = unreadCount;
                            } else {
                                badge.remove();
                                document.getElementById('markAllAsRead').disabled = true;
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                });
            });
        });

        // Marquer toutes les notifications comme lues
        document.getElementById('markAllAsRead').addEventListener('click', function() {
            fetch('{{ path('app_notifications_mark_all_as_read') }}', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mettre à jour l'interface
                    const unreadItems = document.querySelectorAll('.notification-item.unread');
                    unreadItems.forEach(item => {
                        item.classList.remove('unread');
                        item.dataset.read = 'true';
                        const markAsReadButton = item.querySelector('.mark-as-read');
                        if (markAsReadButton) {
                            markAsReadButton.remove();
                        }
                    });

                    // Mettre à jour le compteur
                    const badge = document.querySelector('.notification-header .badge');
                    if (badge) {
                        badge.remove();
                    }

                    this.disabled = true;
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
            });
        });

        // Filtrer les notifications
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Mettre à jour les boutons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filter = this.dataset.filter;
                const notificationItems = document.querySelectorAll('.notification-item');

                notificationItems.forEach(item => {
                    if (filter === 'all') {
                        item.style.display = 'block';
                    } else if (filter === 'unread') {
                        item.style.display = item.dataset.read === 'false' ? 'block' : 'none';
                    } else {
                        item.style.display = item.dataset.severity === filter ? 'block' : 'none';
                    }
                });
            });
        });
    });
</script>
{% endblock %}
