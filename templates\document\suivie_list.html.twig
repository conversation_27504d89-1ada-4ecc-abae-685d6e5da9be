{% extends 'base.html.twig' %}

{% block title %}Suivi des documents{% endblock %}

{% block body %}
<div class="mt-3" style="margin: 0 2%;">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h3 class="text-center mb-0">Suivi des documents</h3>
                
                {# Légende des couleurs #}
                <div class="d-flex justify-content-center gap-3">
                    <span class="badge rounded-pill panier">
                        Panier
                    </span>
                    <span class="badge rounded-pill old-place">
                        Étape Validée
                    </span>
                    <span class="badge rounded-pill current-step">
                        Étape Actuelle
                    </span>
                    <span class="badge rounded-pill visited-place">
                        Prochaine Étape
                    </span>
                </div>
            </div>

            {# Table responsive avec amélioration de style #}
            <div class="table-responsive">
                <table class="table table-hover align-middle shadow-sm">
                    <thead id="table-head">
                        <tr>
                            <th scope="col" class="text-center">Pack</th>
                            <th scope="col" class="text-center">Activity</th>
                            <th scope="col" class="text-center" colspan="2">Reference</th>
                            <th scope="col" class="text-center" colspan="2">Prod Drawing</th>
                            <th scope="col" class="text-center">Type</th>
                            <th scope="col" class="text-center" colspan="2">Commentaires</th>
                            <th scope="col">Cheminement</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for result in results %}
                        <tr>
                            <td class="text-center"><a href="{{ path('detail_package', {'id': result.document.relPack.id}) }}" class="badge bg-primary pack-link">{{result.document.relPack.id}}</a></td>
                            <td class="text-center">{{ result.document.relPack.activity }}</td>
                            <td class="text-center">{{ result.document.reference }}</td>
                            <td class="text-center">{{ result.document.refRev }}</td>
                            <td class="text-center">{{ result.document.prodDraw }}</td>
                            <td class="text-center">{{ result.document.prodDrawRev }}</td>
                            <td class="text-center type">
                                <p class="mb-0">{{ result.document.docType }}</p>
                                <p class="mb-0">{{ result.document.procType }}</p>
                            </td>
                            <td class="text-center px-0">
                                {% if result.document.PrincipalCommentaires|length > 0 %}
                                    <div class="tooltip-container"  data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.PrincipalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-user"></i></span>
                                    </div>
                                {% endif %}
                            </td>
                            <td class="text-center px-0">
                                {% if result.document.GlobalCommentaires|length > 0 %}
                                    <div class="tooltip-container" data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.GlobalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-users"></i></span>
                                    </div>
                                {% endif %}
                            </td>

                            <td class="gap-3 p-3" style="white-space: wrap;">
                                {% for place, val in result.oldPlaces %}
                                    <a 
                                        class="badge rounded-pill old-place" 
                                        style="text-decoration: none;">
                                           {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.oldPlaces %}
                                    <a 
                                        class="badge rounded-pill old-place panier" 
                                        style="text-decoration: none;">
                                           {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}?document-id={{ result.document.id }}#document-id={{ result.document.id }}"
                                       class="badge rounded-pill current-step" 
                                       style="text-decoration: none;">
                                        {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}#onlget={{ place }}&document-id={{ result.document.id }}" 
                                       class="badge rounded-pill current-step panier" 
                                       style="text-decoration: none;">
                                        {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.visitedPlaces %}
                                    <a 
                                        class="badge rounded-pill visited-place" 
                                        style="text-decoration: none;">
                                            {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.paniers.visitedPlaces %}
                                    <a 
                                        class="badge rounded-pill visited-place panier" 
                                        style="text-decoration: none;">
                                            {{ place|replace({'_': ' '})|first|upper ~ place|replace({'_': ' '})|slice(1) }}
                                    </a>
                                {% endfor %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>

            {# Pagination améliorée #}
            <div>
                {{ knp_pagination_render(pagination) }}
            </div>

        </div>
    </div>
</div>

<style>
/* Styles pour les badges */
.old-place {
    background-color: #28a745;
    color: white;
}

.current-step {
    background-color:rgb(0, 17, 255);
    color: white;
}

.visited-place {
    background-color:rgb(73, 161, 255);
    color: white;
}

table thead th {
    font-weight: bold;
}

* {
    user-select: none;
}

td, span {
    font-size: 0.90rem;
    white-space: nowrap;
}

.type{
    font-size: 0.75rem;
}

th {
    font-size: 0.95rem;
    white-space: nowrap;
    background-color: #004080!important;
    color: #fff!important;

}
.old-place.panier {
    background: repeating-linear-gradient(
    -55deg,
    #28a745,
    #28a745 10px,
    rgba(40, 167, 69, 0.65) 10px,
    rgba(40, 167, 69, 0.65) 20px
    );
}

.current-step.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(0, 17, 255),
    rgb(0, 17, 255) 10px,
    rgb(0, 17, 255, 0.65) 10px,
    rgb(0, 17, 255, 0.65) 20px
    );
}

.visited-place.panier {
    background: repeating-linear-gradient(
    -55deg,
        rgb(73, 161, 255),
        rgb(73, 161, 255) 10px,
        rgba(73, 161, 255, 0.65) 10px,
        rgb(73, 161, 255, 0.65) 20px
    );
}

.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(95, 95, 95),
    rgb(95, 95, 95) 10px,
    rgba(44, 44, 44, 0.65) 10px,
    rgba(44, 44, 44, 0.65) 20px
);
}

</style>
<script>
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl, {
        customClass: 'custom-tooltip'
    }))
</script>

<style>
.custom-tooltip .tooltip-inner {
    white-space: normal;
    max-width: none;
}
</style>

{% endblock %}
