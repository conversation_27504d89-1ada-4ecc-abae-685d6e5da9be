-- BE_1_condition
-- Sélection dans tbl_released_package
SELECT *
FROM tbl_released_package
WHERE
    TRIM(Creation_VISA) = ''   -- :contentReference[oaicite:0]{index=0}
    AND TRIM(VISA_BE_2)   = ''
    AND TRIM(VISA_BE_3)   = ''
;

-- BE_2_condition
-- Sélection dans tbl_released_package
SELECT *
FROM tbl_released_package
WHERE
    TRIM(Creation_VISA) <> ''
    AND TRIM(VISA_BE_2)   = ''
    AND TRIM(VISA_BE_3)   = ''
;

-- BE_condition
-- Sélection dans tbl_released_package
SELECT *
FROM tbl_released_package
WHERE
    TRIM(Creation_VISA) <> ''
    AND TRIM(VISA_BE_2)   <> ''
    AND TRIM(VISA_BE_3)   = ''
;

-- Product_Conditions
-- Produit
-- Sélection dans tbl_released_drawing (jointure pour utiliser VISA_BE_3)
SELECT d.*
FROM tbl_released_drawing AS d
JOIN tbl_released_package AS p
    ON d.Rel_Pack_Num = p.Rel_Pack_Num  -- 
WHERE
    TRIM(d.VISA_Product)  = ''
    AND TRIM(p.VISA_BE_3)  <> ''
;

-- Inventory_Conditions
-- Inventory (Qual_Logistique) – on considère aussi "Logistique"
-- Sélection dans tbl_released_drawing (jointure pour utiliser VISA_BE_3)
SELECT d.*
FROM tbl_released_drawing AS d
JOIN tbl_released_package AS p
    ON d.Rel_Pack_Num = p.Rel_Pack_Num  -- 
WHERE
    TRIM(d.VISA_Inventory) = ''
    AND TRIM(p.VISA_BE_3)   <> ''
    AND TRIM(d.VISA_GID)    = ''
    AND d.Doc_Type         <> 'DOC'
    AND d.Inventory_Impact <> 'NO IMPACT'
;

-- Quality_Conditions
-- Quality
-- Sélection dans tbl_released_drawing (jointure pour utiliser VISA_BE_3)
SELECT d.*
FROM tbl_released_drawing AS d
JOIN tbl_released_package AS p
    ON d.Rel_Pack_Num = p.Rel_Pack_Num  -- 
WHERE
    (
        d.Doc_Type = 'PUR'
     OR d.Doc_Type = 'ASSY'
     OR d.Doc_Type = 'DOC'
    )
    AND TRIM(p.VISA_BE_3)   <> ''
    AND TRIM(d.VISA_Quality) = ''
;


-- Project_Conditions
-- Project
-- Sélection dans tbl_released_drawing (jointure avec tbl_released_package pour utiliser Project et VISA_BE_3)
SELECT d.*
FROM tbl_released_drawing AS d
JOIN tbl_released_package AS p
    ON d.Rel_Pack_Num = p.Rel_Pack_Num  -- correspondance des releases 
WHERE
    TRIM(d.VISA_Project) = ''
    AND (
        p.Project             <> 'STAND'
        AND TRIM(p.VISA_BE_3) <> ''
        AND (
            d.Prod_Draw LIKE 'GA%' 
            OR d.Prod_Draw LIKE 'FT%'
        )
    )
;

-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
-- METRO_Conditions
-- Metro
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    (
        TRIM(VISA_Prod) <> ''
        AND Proc_Type = 'E'
    )
    OR (
        TRIM(VISA_Quality) <> ''
        AND Doc_Type = 'PUR'
    )
    AND TRIM(VISA_Metro) = ''
;

-- Q_PROD_Conditions
-- QProd
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_Metro) <> ''
    AND (
        Doc_Type = 'MACH'
     OR Doc_Type = 'MOLD'
    )
    AND TRIM(VISA_Q_PROD) = ''
;


-- Prod_ASSY_Conditions
-- Assembly
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_Product) <> ''
    AND (
        Doc_Type = 'ASSY'
        AND LEFT(Proc_Type, 1) <> 'F'
        OR Doc_Type = 'DOC'
    )
    AND TRIM(VISA_Prod) = ''
;


--  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

-- Method_Conditions
-- Methode_assemblage
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_Method) = ''
    AND (
        Doc_Type = 'ASSY'
     OR Doc_Type = 'DOC'
     OR Material_Type = 'PACKAGING'
    )
    AND TRIM(VISA_Prod) <> ''
;


-- Prod_MACH_Conditions
-- Machining
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_Product) <> ''
    AND Doc_Type = 'MACH'
    AND TRIM(VISA_Prod)   = ''
;


-- Prod_MOLD_Conditions
-- Molding
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_Product) <> ''
    AND Doc_Type = 'MOLD'
    AND TRIM(VISA_Prod)   = ''
;


-- PUR_1_RFQ_Conditions
-- Achat_Rfq
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_PUR_1) = ''
    AND Doc_Type = 'PUR'
    AND (
        Proc_Type = ''
        OR Proc_Type = 'F'
        OR Proc_Type = 'F30'
    )
    AND TRIM(VISA_Quality) <> ''
    AND TRIM(VISA_Product) <> ''
;


-- PUR_2_PRISDANS_Conditions
-- Achat_F30
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_PUR_2) = ''
    AND TRIM(VISA_PUR_1) <> ''
    AND Proc_Type = 'F30'
;

-- PUR_3_Conditions
-- Achat_FIA
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    VISA_PUR_1      <> ''
    AND TRIM(VISA_PUR_3) = ''
    AND (
        Proc_Type = 'F'
     OR Proc_Type = 'F30'
    )
    AND TRIM(VISA_GID_2) <> ''
;


-- PUR_4_Conditions
-- Achat_RoHs_REACH
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_PUR_4) = ''
    AND TRIM(VISA_PUR_1) <> ''
;


-- PUR_5_Conditions
-- Achat_Hts
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_PUR_5) = ''
    AND TRIM(VISA_PUR_3) <> ''
;


-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
-- Supply_Conditions
-- Planning
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    (
        Doc_Type = 'ASSY'
     OR Doc_Type = 'MACH'
     OR Doc_Type = 'MOLD'
     OR Doc_Type = 'DOC'
    )
    AND VISA_Prod   <> ''
    AND VISA_Supply = ''
;


-- !!!!!!!!!!!!!!!!! ASSY. rOUTING !!!!!!!!!!!!!!!
-- MOF_Conditions
-- Indus
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    (
        Doc_Type = 'ASSY'
        AND TRIM(VISA_Metro) <> ''
    )
    OR (
        Doc_Type = 'DOC'
    )
    AND TRIM(VISA_Prod) <> ''
    AND TRIM(VISA_MOF)  = ''
;







-- GID_1_Conditions
-- Core_Data
-- Sélection dans tbl_released_drawing (jointure avec tbl_released_package pour utiliser Project et VISA_BE_3, et conditions sur Inventory_Impact)
SELECT d.*
FROM tbl_released_drawing AS d
JOIN tbl_released_package AS p
    ON d.Rel_Pack_Num = p.Rel_Pack_Num  -- 
WHERE
    (
        (
            TRIM(d.VISA_PUR_1) <> ''
            AND d.Proc_Type = 'F'
            AND d.Doc_Type = 'PUR'
        )
        OR (
            TRIM(d.VISA_PUR_2) <> ''
            AND d.Proc_Type = 'F30'
            AND d.Doc_Type = 'PUR'
        )
        OR (
            (
                d.Doc_Type = 'MACH'
             OR d.Doc_Type = 'MOLD'
            )
            AND TRIM(d.VISA_Metro)  <> ''
            AND TRIM(d.VISA_Supply) <> ''
        )
        OR (
            d.Doc_Type = 'ASSY'
            AND TRIM(d.VISA_Quality) <> ''
            AND TRIM(d.VISA_Metro)   <> ''
            AND TRIM(d.VISA_Supply)  <> ''
            AND (
                (
                    p.Project <> 'STAND'
                    AND TRIM(d.VISA_Project) <> ''
                    AND (
                        d.Prod_Draw LIKE 'GA%'
                     OR d.Prod_Draw LIKE 'FT%'
                    )
                )
                OR (
                    p.Project <> 'STAND'
                    AND TRIM(d.VISA_Project) = ''
                    AND (
                        d.Prod_Draw NOT LIKE 'GA%'
                     AND d.Prod_Draw NOT LIKE 'FT%'
                    )
                )
                OR (
                    p.Project = 'STAND'
                    AND TRIM(d.VISA_Project) = ''
                )
            )
        )
        OR (
            d.Doc_Type = 'DOC'
            AND TRIM(d.VISA_Quality) <> ''
            AND TRIM(d.VISA_Metro)   <> ''
            AND (
                (
                    p.Project <> 'STAND'
                    AND TRIM(d.VISA_Project) <> ''
                    AND (
                        d.Prod_Draw LIKE 'GA%'
                     OR d.Prod_Draw LIKE 'FT%'
                    )
                )
                OR (
                    p.Project <> 'STAND'
                    AND TRIM(d.VISA_Project) = ''
                    AND (
                        d.Prod_Draw NOT LIKE 'GA%'
                     AND d.Prod_Draw NOT LIKE 'FT%'
                    )
                )
                OR (
                    p.Project = 'STAND'
                    AND TRIM(d.VISA_Project) = ''
                )
            )
        )
    )
    AND (
        (
            TRIM(d.VISA_Inventory) <> ''
            AND (
                d.Inventory_Impact = 'TO BE SCRAPPED'
             OR d.Inventory_Impact = 'TO BE UPDATED'
            )
        )
        OR (
            TRIM(d.VISA_Inventory) = ''
            AND (
                d.Inventory_Impact = 'NO IMPACT'
             OR d.Doc_Type = 'DOC'
            )
        )
    )
    AND TRIM(d.VISA_GID) = ''
;


-- GID_2_Conditions
-- Prod_Data
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    TRIM(VISA_GID)   <> ''
    AND TRIM(VISA_GID_2) = ''
;


-- LABO_Conditions = Laboratory
-- methode_Labo
-- LABO_Conditions
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    Doc_Type = 'ASSY'
    AND TRIM(VISA_MOF) <> ''
    AND TRIM(VISA_LABO) = ''
;

-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
-- ROUTING_ENTRY_Conditions
-- GID
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    (
        Doc_Type = 'ASSY'
        AND TRIM(VISA_MOF) <> ''
    )
    OR (
        (
            Doc_Type = 'MOLD'
         OR Doc_Type = 'MACH'
        )
        AND TRIM(VISA_Prod) <> ''
    )
    AND TRIM(VISA_GID_2)        <> ''
    AND TRIM(VISA_ROUTING_ENTRY) = ''
;


-- Finance_Conditions
-- Costing
-- Sélection dans tbl_released_drawing
SELECT *
FROM tbl_released_drawing
WHERE
    Doc_Type <> 'DOC'
    AND TRIM(VISA_Finance) = ''
    AND (
        (
            TRIM(VISA_PUR_3) <> ''
            AND Doc_Type = 'PUR'
        )
        OR (
            TRIM(VISA_ROUTING_ENTRY) <> ''
            AND Doc_Type <> 'PUR'
        )
    )
;
