<?php

namespace App\Entity;

use App\Repository\DocumentRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: DocumentRepository::class)]
class Document
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $reference = null;

    #[ORM\Column(length: 255)]
    private ?string $refRev = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $refTitleFra = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prodDraw = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prodDrawRev = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $alias = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $docType = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Material_Type = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $procType = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $inventory_impact = null;

    #[ORM\ManyToOne(inversedBy: 'documents')]
    private ?ReleasedPackage $relPack = null;

    /**
     * @var Collection<int, DocumentStatusHistory>
     */
    #[ORM\OneToMany(targetEntity: DocumentStatusHistory::class, mappedBy: 'document')]
    private Collection $documentStatusHistories;

    /**
     * @var Collection<int, Visa>
     */
    #[ORM\OneToMany(targetEntity: Visa::class, mappedBy: 'releasedDrawing', orphanRemoval: true)]
    private Collection $visas;

    #[ORM\Column(type: 'json')]
    private array $currentSteps = [];

    #[ORM\Column(nullable: true)]
    private ?int $idAletiq = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $CustDrawing = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $CustDrawingRev = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $action = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $ex = null;

    #[ORM\Column(nullable: true)]
    private ?int $Weight = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $WeightUnit = null;

    #[ORM\Column(nullable: true)]
    private ?int $PlatingSurface = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $PlatingSurfaceUnit = null;

    #[ORM\Column(nullable: true)]
    private ?bool $internalMachRec = null;

    #[ORM\Column(nullable: true)]
    private ?int $cls = null;

    #[ORM\Column(nullable: true)]
    private ?int $moq = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $productCode = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prodAgent = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $mof = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $commodityCode = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $purchasingGroup = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $matProdType = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Unit = null;

    #[ORM\Column(nullable: true)]
    private ?int $leadtime = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prisDans1 = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prisDans2 = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $eccn = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $rdo = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $hts = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $fia = null;

    #[ORM\Column(nullable: true)]
    private ?int $metroTime = null;



    /**
     * @var list<string>|null
     */
    #[ORM\Column(nullable: true)]
    private ?array $qInspection = null;


    #[ORM\Column(length: 255, nullable: true)]
    private ?string $qDynamization = null;

    /**
     * @var list<string>|null
     */
    #[ORM\Column(nullable: true)]
    private ?array $qDocRec = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $qControlRouting = null;

    #[ORM\Column(nullable: true)]
    private ?int $criticalComplete = null;

    #[ORM\Column(nullable: true)]
    private ?bool $switchAletiq = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $material = null;

    /**
     * @var Collection<int, Commentaire>
     */
    #[ORM\OneToMany(targetEntity: Commentaire::class, mappedBy: 'documents')]
    private Collection $commentaires;

    /**
     * @var list<string>|null
     */
    #[ORM\Column(nullable: true)]
    private ?array $metroControl = null;

    #[ORM\Column]
    private ?bool $docImpact = null;

    #[ORM\ManyToOne(inversedBy: 'documents')]
    private ?User $superviseur = null;

    #[ORM\Column(nullable: true)]
    private ?array $stateTimestamps = null;

    #[ORM\Column(nullable: true)]
    private ?array $updates = null;

    public function __construct()
    {
        $this->documentStatusHistories = new ArrayCollection();
        $this->visas = new ArrayCollection();
        $this->commentaires = new ArrayCollection();
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): static
    {
        $this->reference = $reference;

        return $this;
    }

    public function getRefRev(): ?string
    {
        return $this->refRev;
    }

    public function setRefRev(string $refRev): static
    {
        $this->refRev = $refRev;

        return $this;
    }

    public function getRefTitleFra(): ?string
    {
        return $this->refTitleFra;
    }

    public function setRefTitleFra(?string $refTitleFra): static
    {
        $this->refTitleFra = $refTitleFra;

        return $this;
    }

    public function getProdDraw(): ?string
    {
        return $this->prodDraw;
    }

    public function setProdDraw(?string $prodDraw): static
    {
        $this->prodDraw = $prodDraw;

        return $this;
    }

    public function getProdDrawRev(): ?string
    {
        return $this->prodDrawRev;
    }

    public function setProdDrawRev(?string $prodDrawRev): static
    {
        $this->prodDrawRev = $prodDrawRev;

        return $this;
    }


    public function getAlias(): ?string
    {
        return $this->alias;
    }

    public function setAlias(?string $alias): static
    {
        $this->alias = $alias;

        return $this;
    }

    public function getDocType(): ?string
    {
        return $this->docType;
    }

    public function setDocType(?string $docType): static
    {
        $this->docType = $docType;

        return $this;
    }

    public function getRelPack(): ?ReleasedPackage
    {
        return $this->relPack;
    }

    public function setRelPack(?ReleasedPackage $relPack): static
    {
        $this->relPack = $relPack;

        return $this;
    }

    /**
     * @return Collection<int, DocumentStatusHistory>
     */
    public function getDocumentStatusHistories(): Collection
    {
        return $this->documentStatusHistories;
    }

    public function addDocumentStatusHistory(DocumentStatusHistory $documentStatusHistory): static
    {
        if (!$this->documentStatusHistories->contains($documentStatusHistory)) {
            $this->documentStatusHistories->add($documentStatusHistory);
            $documentStatusHistory->setDocument($this);
        }

        return $this;
    }

    public function removeDocumentStatusHistory(DocumentStatusHistory $documentStatusHistory): static
    {
        if ($this->documentStatusHistories->removeElement($documentStatusHistory)) {
            // set the owning side to null (unless already changed)
            if ($documentStatusHistory->getDocument() === $this) {
                $documentStatusHistory->setDocument(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Visa>
     */
    public function getVisas(): Collection
    {
        return $this->visas;
    }

    public function getVisasArray(): array
    {
        $visas = [];
        foreach ($this->visas as $visa) {
            $visas[] = $visa->getName();
        }
        return $visas;
    }

    public function addVisa(Visa $visa): static
    {
        if (!$this->visas->contains($visa)) {
            $this->visas->add($visa);
            $visa->setReleasedDrawing($this);
        }

        return $this;
    }

    public function removeVisa(Visa $visa): static
    {
        if ($this->visas->removeElement($visa)) {
            // set the owning side to null (unless already changed)
            if ($visa->getReleasedDrawing() === $this) {
                $visa->setReleasedDrawing(null);
            }
        }

        return $this;
    }

    public function hasVisa(string $visaName): bool
    {
        foreach ($this->visas as $visa) {
            if ($visa->getName() === $visaName) {
                return true;
            }
        }
        return false;
    }

    public function hasVisaLogistique(): string
    {
        if($this->hasVisa('visa_Qual_Logistique') ) {
            return 'Logistique';
        }else{
            return 'Qualité';
        }
    }

    public function getCurrentSteps(): array
    {
        return $this->currentSteps;
    }

    public function getCurrentStepsVisa(): array
    {
        $currentSteps = $this->currentSteps;
        $visas = $this->getVisasArray();
        foreach ($currentSteps as $key => $value) {
            if (in_array("visa_$key", $visas)) {
                unset($currentSteps[$key]);
            }
        }
        return $currentSteps;
    }

    public function setCurrentSteps(array $currentSteps): self
    {
        $this->currentSteps = $currentSteps;

        return $this;
    }

    public function getInventoryImpact(): ?string
    {
        return $this->inventory_impact;
    }

    public function setInventoryImpact(?string $inventory_impact): self
    {
        $this->inventory_impact = $inventory_impact;

        return $this;
    }

    public function getProcType(): ?string
    {
        return $this->procType;
    }

    public function setProcType(?string $procType): self
    {
        $this->procType = $procType;

        return $this;
    }

    public function getMaterialType(): ?string
    {
        return $this->Material_Type;
    }

    public function setMaterialType(?string $Material_Type): self
    {
        $this->Material_Type = $Material_Type;

        return $this;
    }

    public function getIdAletiq(): ?int
    {
        return $this->idAletiq;
    }

    public function setIdAletiq(?int $idAletiq): static
    {
        $this->idAletiq = $idAletiq;

        return $this;
    }

    public function getCustDrawing(): ?string
    {
        return $this->CustDrawing;
    }

    public function setCustDrawing(?string $CustDrawing): static
    {
        $this->CustDrawing = $CustDrawing;

        return $this;
    }

    public function getCustDrawingRev(): ?string
    {
        return $this->CustDrawingRev;
    }

    public function setCustDrawingRev(?string $CustDrawingRev): static
    {
        $this->CustDrawingRev = $CustDrawingRev;

        return $this;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function setAction(?string $action): static
    {
        $this->action = $action;

        return $this;
    }

    public function getEx(): ?string
    {
        return $this->ex;
    }

    public function setEx(?string $ex): static
    {
        $this->ex = $ex;

        return $this;
    }

    public function getWeight(): ?int
    {
        return $this->Weight;
    }

    public function setWeight(?int $Weight): static
    {
        $this->Weight = $Weight;

        return $this;
    }

    public function getWeightUnit(): ?string
    {
        return $this->WeightUnit;
    }

    public function setWeightUnit(?string $WeightUnit): static
    {
        $this->WeightUnit = $WeightUnit;

        return $this;
    }

    public function getPlatingSurface(): ?int
    {
        return $this->PlatingSurface;
    }

    public function setPlatingSurface(?int $PlatingSurface): static
    {
        $this->PlatingSurface = $PlatingSurface;

        return $this;
    }

    public function getPlatingSurfaceUnit(): ?string
    {
        return $this->PlatingSurfaceUnit;
    }

    public function setPlatingSurfaceUnit(?string $PlatingSurfaceUnit): static
    {
        $this->PlatingSurfaceUnit = $PlatingSurfaceUnit;

        return $this;
    }

    public function isInternalMachRec(): ?bool
    {
        return $this->internalMachRec;
    }

    public function setInternalMachRec(?bool $internalMachRec): static
    {
        $this->internalMachRec = $internalMachRec;

        return $this;
    }

    public function getCls(): ?int
    {
        return $this->cls;
    }

    public function setCls(?int $cls): static
    {
        $this->cls = $cls;

        return $this;
    }

    public function getMoq(): ?int
    {
        return $this->moq;
    }

    public function setMoq(?int $moq): static
    {
        $this->moq = $moq;

        return $this;
    }

    public function getProductCode(): ?string
    {
        return $this->productCode;
    }

    public function setProductCode(?string $productCode): static
    {
        $this->productCode = $productCode;

        return $this;
    }

    public function getProdAgent(): ?string
    {
        return $this->prodAgent;
    }

    public function setProdAgent(?string $prodAgent): static
    {
        $this->prodAgent = $prodAgent;

        return $this;
    }

    public function getMof(): ?string
    {
        return $this->mof;
    }

    public function setMof(?string $mof): static
    {
        $this->mof = $mof;

        return $this;
    }

    public function getCommodityCode(): ?string
    {
        return $this->commodityCode;
    }

    public function setCommodityCode(?string $commodityCode): static
    {
        $this->commodityCode = $commodityCode;

        return $this;
    }

    public function getPurchasingGroup(): ?string
    {
        return $this->purchasingGroup;
    }

    public function setPurchasingGroup(?string $purchasingGroup): static
    {
        $this->purchasingGroup = $purchasingGroup;

        return $this;
    }

    public function getMatProdType(): ?string
    {
        return $this->matProdType;
    }

    public function setMatProdType(?string $matProdType): static
    {
        $this->matProdType = $matProdType;

        return $this;
    }

    public function getUnit(): ?string
    {
        return $this->Unit;
    }

    public function setUnit(?string $Unit): static
    {
        $this->Unit = $Unit;

        return $this;
    }

    public function getLeadtime(): ?int
    {
        return $this->leadtime;
    }

    public function setLeadtime(?int $leadtime): static
    {
        $this->leadtime = $leadtime;

        return $this;
    }

    public function getPrisDans1(): ?string
    {
        return $this->prisDans1;
    }

    public function setPrisDans1(?string $prisDans1): static
    {
        $this->prisDans1 = $prisDans1;

        return $this;
    }

    public function getPrisDans2(): ?string
    {
        return $this->prisDans2;
    }

    public function setPrisDans2(?string $prisDans2): static
    {
        $this->prisDans2 = $prisDans2;

        return $this;
    }

    public function getEccn(): ?string
    {
        return $this->eccn;
    }

    public function setEccn(?string $eccn): static
    {
        $this->eccn = $eccn;

        return $this;
    }

    public function getRdo(): ?string
    {
        return $this->rdo;
    }

    public function setRdo(?string $rdo): static
    {
        $this->rdo = $rdo;

        return $this;
    }

    public function getHts(): ?string
    {
        return $this->hts;
    }

    public function setHts(?string $hts): static
    {
        $this->hts = $hts;

        return $this;
    }

    public function getFia(): ?string
    {
        return $this->fia;
    }

    public function setFia(?string $fia): static
    {
        $this->fia = $fia;

        return $this;
    }

    public function getMetroTime(): ?int
    {
        return $this->metroTime;
    }

    public function setMetroTime(?int $metroTime): static
    {
        $this->metroTime = $metroTime;

        return $this;
    }

    public function getQInspection(): ?array
    {
        return $this->qInspection;
    }

    public function setQInspection(?array $qInspection): static
    {
        $this->qInspection = $qInspection;

        return $this;
    }


    public function getQDynamization(): ?string
    {
        return $this->qDynamization;
    }

    public function setQDynamization(?string $qDynamization): static
    {
        $this->qDynamization = $qDynamization;

        return $this;
    }

    public function getQDocRec(): ?array
    {
        return $this->qDocRec;
    }

    public function setQDocRec(?array $qDocRec): static
    {
        $this->qDocRec = $qDocRec;

        return $this;
    }

    public function getQControlRouting(): ?string
    {
        return $this->qControlRouting;
    }

    public function setQControlRouting(?string $qControlRouting): static
    {
        $this->qControlRouting = $qControlRouting;

        return $this;
    }

    public function getCriticalComplete(): ?int
    {
        return $this->criticalComplete;
    }

    public function setCriticalComplete(?int $criticalComplete): static
    {
        $this->criticalComplete = $criticalComplete;

        return $this;
    }

    public function isSwitchAletiq(): ?bool
    {
        return $this->switchAletiq;
    }

    public function setSwitchAletiq(?bool $switchAletiq): static
    {
        $this->switchAletiq = $switchAletiq;

        return $this;
    }

    public function getMaterial(): ?string
    {
        return $this->material;
    }

    public function setMaterial(?string $material): static
    {
        $this->material = $material;

        return $this;
    }

    /**
     * @return Collection<int, Commentaire>
     */
    public function getCommentaires(): Collection
    {
        return $this->commentaires;
    }

    public function addCommentaire(Commentaire $commentaire): static
    {
        if (!$this->commentaires->contains($commentaire)) {
            $this->commentaires->add($commentaire);
            $commentaire->addDocument($this);
        }

        return $this;
    }

    public function removeCommentaire(Commentaire $commentaire): static
    {
        if ($this->commentaires->removeElement($commentaire)) {
            $commentaire->removeDocument($this);
        }

        return $this;
    }

    /**
     * @return list<string>
     */
    public function getMetroControl(): ?array
    {
        $metroControl = $this->metroControl;
        return $metroControl;
    }

    /**
     * @param list<string> $metroControl
     */
    public function setMetroControl(array $metroControl): static
    {
        $this->metroControl = $metroControl;

        return $this;
    }

    public function isDocImpact(): ?bool
    {
        return $this->docImpact;
    }

    public function setDocImpact(bool $docImpact): static
    {
        $this->docImpact = $docImpact;

        return $this;
    }

    public function getSuperviseur(): ?User
    {
        return $this->superviseur;
    }

    public function setSuperviseur(?User $superviseur): static
    {
        $this->superviseur = $superviseur;

        return $this;
    }

    public function getVisasValid(): array
    {
        $visas = [];
        foreach ($this->visas as $visa) {
            if ($visa->getStatus() === 'valid') {
                $visas[] = $visa;
            }
        }
        return $visas;
    }

    // commentaire
    public function getAllCommentaires(): array
    {
        $commentaires = [];
        foreach ($this->commentaires as $commentaire) {
            // too string
            $commentaires[] = $commentaire->__toString();
        }
        return $commentaires;
    }

    public function getPrincipalCommentaires(): array
    {
        $commentaires = [];
        foreach ($this->commentaires as $commentaire) {
            if ($commentaire->getType() === 'principal') {
                $commentaires[] = $commentaire->__toString();
            }
        }
        return $commentaires;
    }

    public function getGlobalCommentaires(): array
    {
        $commentaires = [];
        foreach ($this->commentaires as $commentaire) {
            if ($commentaire->getType() === 'secondaire') {
                $commentaires[] = $commentaire->__toString();
            }
        }
        return $commentaires;
    }

    public function hasGIDCommentaire(): bool
    {
        foreach ($this->commentaires as $commentaire) {
            if ($commentaire->getState() === 'Core_Data') {
                return true;
            }
        }
        return false;
    }

    public function hasGID2Commentaire(): bool
    {
        foreach ($this->commentaires as $commentaire) {
            if ($commentaire->getState() === 'Prod_Data') {
                return true;
            }
        }
        return false;
    }

    public function getGIDCommentaire(): ?array
    {
        foreach ($this->commentaires as $commentaire) {
            if ($commentaire->getState() === 'Core_Data') {
                return [
                    'id' => $commentaire->getId(),
                    'commentaire' => $commentaire->getCommentaire()
                ];
            }
        }
        return null;
    }

    public function getGID2Commentaire(): ?array
    {
        foreach ($this->commentaires as $commentaire) {
            if ($commentaire->getState() === 'Prod_Data') {
                return [
                    'id' => $commentaire->getId(),
                    'commentaire' => $commentaire->getCommentaire()
                ];
            }
        }
        return null;
    }

    public function toJson(): array
    {
        return [
            'id' => $this->getId(),
            'reference' => $this->getReference(),
            'refRev' => $this->getRefRev(),
            'refTitleFra' => $this->getRefTitleFra(),
            'prodDraw' => $this->getProdDraw(),
            'prodDrawRev' => $this->getProdDrawRev(),
            'alias' => $this->getAlias(),
            'docType' => $this->getDocType(),
            'Material_Type' => $this->getMaterialType(),
            'procType' => $this->getProcType(),
            'inventory_impact' => $this->getInventoryImpact(),
            'relPack' => $this->getRelPack() ? $this->getRelPack()->getId() : null,
            'relPackDescription' => $this->getRelPack() ? $this->getRelPack()->getDescription() : null,
            'currentSteps' => $this->getCurrentSteps(),
            'idAletiq' => $this->getIdAletiq(),
            'CustDrawing' => $this->getCustDrawing(),
            'CustDrawingRev' => $this->getCustDrawingRev(),
            'action' => $this->getAction(),
            'ex' => $this->getEx(),
            'exPack' => $this->getRelPack() ? $this->getRelPack()->getEx() : null,
            'Weight' => $this->getWeight(),
            'WeightUnit' => $this->getWeightUnit(),
            'PlatingSurface' => $this->getPlatingSurface(),
            'PlatingSurfaceUnit' => $this->getPlatingSurfaceUnit(),
            'internalMachRec' => $this->isInternalMachRec(),
            'cls' => $this->getCls(),
            'moq' => $this->getMoq(),
            'productCode' => $this->getProductCode(),
            'prodAgent' => $this->getProdAgent(),
            'mof' => $this->getMof(),
            'commodityCode' => $this->getCommodityCode(),
            'purchasingGroup' => $this->getPurchasingGroup(),
            'matProdType' => $this->getMatProdType(),
            'Unit' => $this->getUnit(),
            'leadtime' => $this->getLeadtime(),
            'prisDans1' => $this->getPrisDans1(),
            'prisDans2' => $this->getPrisDans2(),
            'eccn' => $this->getEccn(),
            'rdo' => $this->getRdo(),
            'hts' => $this->getHts(),
            'fia' => $this->getFia(),
            'metroTime' => $this->getMetroTime(),
            'qInspection' => $this->getQInspection(),
            'qDynamization' => $this->getQDynamization(),
            'qDocRec' => $this->getQDocRec(),
            'qControlRouting' => $this->getQControlRouting(),
            'criticalComplete' => $this->getCriticalComplete(),
            'switchAletiq' => $this->isSwitchAletiq(),
            'material' => $this->getMaterial(),
            'metroControl' => $this->getMetroControl(),
            'docImpact' => $this->isDocImpact(),
            'superviseur' => $this->getSuperviseur() ? $this->getSuperviseur() : null,
            'commentaires' => $this->getGlobalCommentaires(),
            'commentaires_By_Owner' => $this->getPrincipalCommentaires(),
            'stateTimestamps' => $this->getStateTimestamps(),
            'project' => $this->getRelPack() && $this->getRelPack()->getProjectRelation() ? $this->getRelPack()->getProjectRelation()->getOTP() : 'STAND',
            'activity' => $this->getRelPack() ? $this->getRelPack()->getActivity() : null,
            'GID' => $this->getGIDCommentaire(),
            'GID2' => $this->getGID2Commentaire(),
        ];
    }

    /**
     * Pour la compatibilité avec le code existant
     */
    public function getStateTimestamps(): ?array
    {
        if ($this->stateTimestamps === null) {
            return null;
        }

        // Si c'est déjà l'ancien format, retourner tel quel
        $isOldFormat = true;
        foreach ($this->stateTimestamps as $value) {
            if (!is_string($value)) {
                $isOldFormat = false;
                break;
            }
        }

        if ($isOldFormat) {
            return $this->stateTimestamps;
        }

        // Convertir le nouveau format en ancien format pour la compatibilité
        $result = [];
        foreach ($this->stateTimestamps as $state => $entries) {
            if (is_string($entries)) {
                // Déjà au format ancien
                $result[$state] = $entries;
            } elseif (!empty($entries)) {
                // Utiliser la date d'entrée la plus récente pour la compatibilité
                $lastEntry = end($entries);
                $result[$state] = $lastEntry['enter'];
            }
        }

        return $result;
    }

    /**
     * Retourne les timestamps bruts au nouveau format
     */
    public function getRawStateTimestamps(): ?array
    {
        return $this->stateTimestamps;
    }

    public function getAllStates(): array
    {
        return array_keys($this->stateTimestamps ?? []);
    }

    public function setStateTimestamps(?array $stateTimestamps): static
    {
        $this->stateTimestamps = $stateTimestamps;
        return $this;
    }

    /**
     * Enregistre l'entrée dans un état
     */
    public function addStateEnter(string $state, ?User $user = null): void
    {
        // Migrer vers le nouveau format si nécessaire
        $this->migrateStateTimestampsToNewFormat();

        if ($this->stateTimestamps === null) {
            $this->stateTimestamps = [];
        }

        $fromState = null;

        if (!isset($this->stateTimestamps[$state])) {
            $this->stateTimestamps[$state] = [];
        } else {
            // Vérifier si la dernière entrée a une date de sortie
            // Si non, c'est que le document est déjà dans cet état, donc on ferme l'entrée précédente
            if (!empty($this->stateTimestamps[$state])) {
                $lastIndex = count($this->stateTimestamps[$state]) - 1;
                $lastEntry = $this->stateTimestamps[$state][$lastIndex];

                if ($lastEntry['exit'] === null) {
                    // Fermer l'entrée précédente avant d'en créer une nouvelle
                    $this->stateTimestamps[$state][$lastIndex]['exit'] = (new \DateTime())->format('Y-m-d H:i:s');
                }
            }
        }

        // Trouver l'état précédent
        foreach ($this->stateTimestamps as $oldState => $entries) {
            if ($oldState !== $state && !empty($entries)) {
                $lastIndex = count($entries) - 1;
                if (isset($entries[$lastIndex]['exit']) && $entries[$lastIndex]['exit'] === null) {
                    $fromState = $oldState;
                    // Fermer l'entrée de l'état précédent
                    $this->stateTimestamps[$oldState][$lastIndex]['exit'] = (new \DateTime())->format('Y-m-d H:i:s');
                }
            }
        }

        // Ajouter une nouvelle entrée
        $this->stateTimestamps[$state][] = [
            'enter' => (new \DateTime())->format('Y-m-d H:i:s'),
            'exit' => null,
            'from_state' => $fromState
        ];

        // Ajouter une entrée dans l'historique des mises à jour
        if ($fromState) {
            $this->addUpdate('state_change', $user, 'Changement d\'état de ' . $fromState . ' à ' . $state);
        } else {
            $this->addUpdate('state_change', $user, 'Entrée dans l\'état ' . $state);
        }
    }

    /**
     * Migre les données de l'ancien format vers le nouveau format
     */
    private function migrateStateTimestampsToNewFormat(): void
    {
        if ($this->stateTimestamps === null) {
            return;
        }

        // Vérifier si c'est l'ancien format
        $isOldFormat = false;
        foreach ($this->stateTimestamps as $value) {
            if (is_string($value)) {
                $isOldFormat = true;
                break;
            }
        }

        if (!$isOldFormat) {
            return; // Déjà au nouveau format
        }

        // Convertir de l'ancien format vers le nouveau
        $newFormat = [];
        foreach ($this->stateTimestamps as $state => $timestamp) {
            if (is_string($timestamp)) {
                $newFormat[$state] = [
                    [
                        'enter' => $timestamp,
                        'exit' => null
                    ]
                ];
            } else {
                $newFormat[$state] = $timestamp; // Déjà au nouveau format
            }
        }

        $this->stateTimestamps = $newFormat;
    }

    /**
     * Enregistre la sortie d'un état
     */
    public function addStateExit(string $state): void
    {
        // Migrer vers le nouveau format si nécessaire
        $this->migrateStateTimestampsToNewFormat();

        if ($this->stateTimestamps === null || !isset($this->stateTimestamps[$state]) || empty($this->stateTimestamps[$state])) {
            return;
        }

        // Mettre à jour la dernière entrée avec la date de sortie
        $lastIndex = count($this->stateTimestamps[$state]) - 1;
        $this->stateTimestamps[$state][$lastIndex]['exit'] = (new \DateTime())->format('Y-m-d H:i:s');
    }

    /**
     * Pour la compatibilité avec le code existant
     */
    public function addStateTimestamp(string $state, ?User $user = null): void
    {
        $this->addStateEnter($state, $user);
    }

    /**
     * Récupère l'historique des mises à jour
     */
    public function getUpdates(): ?array
    {
        return $this->updates;
    }

    /**
     * Définit l'historique des mises à jour
     */
    public function setUpdates(?array $updates): static
    {
        $this->updates = $updates;
        return $this;
    }

    /**
     * Ajoute une mise à jour à l'historique
     */
    public function addUpdate(string $type, ?User $user = null, ?string $details = null): static
    {
        if ($this->updates === null) {
            $this->updates = [];
        }

        $this->updates[] = [
            'type' => $type,
            'date' => (new \DateTime())->format('Y-m-d H:i:s'),
            'user_id' => $user ? $user->getId() : null,
            'user_name' => $user ? $user->getPrenom() . ' ' . $user->getNom() : null,
            'details' => $details
        ];

        return $this;
    }

    /**
     * Calcule le nombre de jours passés dans l'état actuel depuis la dernière entrée
     * Si le document est sorti puis revenu dans l'état, le compteur recommence à zéro
     */
    public function getDaysInState(string $state): ?int
    {
        if (!isset($this->stateTimestamps[$state])) {
            return null;
        }

        // Gestion de l'ancien format (chaîne de caractères directement)
        if (is_string($this->stateTimestamps[$state])) {
            // Vérifier si le document est actuellement dans cet état
            $isInState = false;
            if ($this->currentSteps && isset($this->currentSteps[$state])) {
                $isInState = true;
            }

            if (!$isInState) {
                return null; // Le document n'est plus dans cet état
            }

            $stateTimestamp = new \DateTime($this->stateTimestamps[$state]);
            $now = new \DateTime();
            return $now->diff($stateTimestamp)->days;
        }

        // Gestion du nouveau format (tableau d'entrées/sorties)
        if (empty($this->stateTimestamps[$state])) {
            return null;
        }

        // Récupérer la dernière entrée
        $lastEntry = end($this->stateTimestamps[$state]);

        // Si la date de sortie est définie, le document n'est plus dans cet état
        if ($lastEntry['exit'] !== null) {
            return null;
        }

        $stateTimestamp = new \DateTime($lastEntry['enter']);
        $now = new \DateTime();

        return $now->diff($stateTimestamp)->days;
    }

    /**
     * Calcule le temps total passé dans un état (en jours)
     */
    public function getTotalDaysInState(string $state): ?int
    {
        if (!isset($this->stateTimestamps[$state])) {
            return null;
        }

        // Gestion de l'ancien format (chaîne de caractères directement)
        if (is_string($this->stateTimestamps[$state])) {
            $stateTimestamp = new \DateTime($this->stateTimestamps[$state]);
            $now = new \DateTime();
            return $now->diff($stateTimestamp)->days;
        }

        // Gestion du nouveau format (tableau d'entrées/sorties)
        if (empty($this->stateTimestamps[$state])) {
            return null;
        }

        $totalDays = 0;
        $now = new \DateTime();

        foreach ($this->stateTimestamps[$state] as $entry) {
            $enterDate = new \DateTime($entry['enter']);

            if (!isset($entry['exit']) || $entry['exit'] === null) {
                // Le document est toujours dans cet état ou la clé 'exit' n'est pas définie
                $exitDate = $now;
            } else {
                $exitDate = new \DateTime($entry['exit']);
            }

            $interval = $exitDate->diff($enterDate);
            $totalDays += $interval->days;
        }

        return $totalDays;
    }

    /**
     * Calcule le temps total depuis la sortie de BE
     */
    public function getDaysSinceBE(): ?int
    {
        if (!isset($this->stateTimestamps['BE'])) {
            return null;
        }

        // Gestion de l'ancien format (chaîne de caractères directement)
        if (is_string($this->stateTimestamps['BE'])) {
            // Dans l'ancien format, on ne peut pas savoir quand le document est sorti de BE
            // On va vérifier si le document est dans un état après BE
            $workflowOrder = [
                'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
                'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
                'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
                'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
                'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
                'Tirage_Plans'
            ];

            $beIndex = array_search('BE', $workflowOrder);
            $isAfterBE = false;

            foreach ($this->stateTimestamps as $state => $timestamp) {
                if (is_string($timestamp) && in_array($state, $workflowOrder)) {
                    $stateIndex = array_search($state, $workflowOrder);
                    if ($stateIndex > $beIndex) {
                        $isAfterBE = true;
                        break;
                    }
                }
            }

            if (!$isAfterBE) {
                return null; // Pas encore sorti de BE
            }

            // Utiliser la date d'entrée dans BE comme approximation
            $beDate = new \DateTime($this->stateTimestamps['BE']);
            $now = new \DateTime();
            return $now->diff($beDate)->days;
        }

        // Gestion du nouveau format (tableau d'entrées/sorties)
        if (empty($this->stateTimestamps['BE'])) {
            return null;
        }

        // Trouver la dernière sortie de BE
        $lastBEExit = null;
        foreach ($this->stateTimestamps['BE'] as $entry) {
            if (isset($entry['exit']) && $entry['exit'] !== null) {
                $lastBEExit = $entry['exit'];
            }
        }

        if ($lastBEExit === null) {
            return null; // Jamais sorti de BE
        }

        $exitDate = new \DateTime($lastBEExit);
        $now = new \DateTime();

        return $now->diff($exitDate)->days;
    }



}
