<?php

namespace App\Service;

use App\Entity\Document;
use App\Entity\Notification;
use App\Entity\User;
use App\Repository\NotificationRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;

class NotificationService
{
    private EntityManagerInterface $entityManager;
    private NotificationRepository $notificationRepository;
    private UserRepository $userRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        NotificationRepository $notificationRepository,
        UserRepository $userRepository
    ) {
        $this->entityManager = $entityManager;
        $this->notificationRepository = $notificationRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Crée une notification pour un utilisateur
     */
    public function createNotification(
        User $user,
        string $type,
        string $title,
        string $message,
        ?Document $relatedDocument = null,
        ?string $link = null,
        string $severity = 'info'
    ): Notification {
        $notification = new Notification();
        $notification->setUser($user);
        $notification->setType($type);
        $notification->setTitle($title);
        $notification->setMessage($message);
        $notification->setRelatedDocument($relatedDocument);
        $notification->setLink($link);
        $notification->setSeverity($severity);

        $this->entityManager->persist($notification);
        $this->entityManager->flush();

        return $notification;
    }

    /**
     * Crée une notification pour tous les utilisateurs d'un département
     */
    public function notifyDepartment(
        string $department,
        string $type,
        string $title,
        string $message,
        ?Document $relatedDocument = null,
        ?string $link = null,
        string $severity = 'info'
    ): array {
        $users = $this->userRepository->findByDepartment($department);
        $notifications = [];

        foreach ($users as $user) {
            $notifications[] = $this->createNotification(
                $user,
                $type,
                $title,
                $message,
                $relatedDocument,
                $link,
                $severity
            );
        }

        return $notifications;
    }

    /**
     * Crée une notification pour tous les utilisateurs ayant un rôle spécifique
     */
    public function notifyRole(
        string $role,
        string $type,
        string $title,
        string $message,
        ?Document $relatedDocument = null,
        ?string $link = null,
        string $severity = 'info'
    ): array {
        $users = $this->userRepository->findByRole($role);
        $notifications = [];

        foreach ($users as $user) {
            $notifications[] = $this->createNotification(
                $user,
                $type,
                $title,
                $message,
                $relatedDocument,
                $link,
                $severity
            );
        }

        return $notifications;
    }

    /**
     * Marque une notification comme lue
     */
    public function markAsRead(Notification $notification): void
    {
        $notification->setIsRead(true);
        $this->entityManager->flush();
    }

    /**
     * Marque toutes les notifications d'un utilisateur comme lues
     */
    public function markAllAsRead(User $user): void
    {
        $this->notificationRepository->markAllAsRead($user);
    }

    /**
     * Récupère les notifications non lues d'un utilisateur
     */
    public function getUnreadNotifications(User $user, int $limit = 10): array
    {
        return $this->notificationRepository->findUnreadByUser($user, $limit);
    }

    /**
     * Compte les notifications non lues d'un utilisateur
     */
    public function countUnreadNotifications(User $user): int
    {
        return $this->notificationRepository->countUnreadByUser($user);
    }

    /**
     * Récupère toutes les notifications d'un utilisateur
     */
    public function getAllNotifications(User $user, int $limit = 50): array
    {
        return $this->notificationRepository->findAllByUser($user, $limit);
    }

    /**
     * Crée une notification pour les documents stagnants
     */
    public function createStagnantDocumentNotification(Document $document, int $daysInState, string $state): void
    {
        $superviseur = $document->getSuperviseur();
        
        if (!$superviseur) {
            return;
        }
        
        $title = 'Document stagnant';
        $message = sprintf(
            'Le document %s (Ref: %s) est dans l\'état "%s" depuis %d jours.',
            $document->getRefTitleFra() ?? 'sans titre',
            $document->getReference(),
            $state,
            $daysInState
        );
        
        $link = '/document/' . $document->getId();
        
        $severity = $daysInState > 14 ? 'danger' : ($daysInState > 7 ? 'warning' : 'info');
        
        $this->createNotification(
            $superviseur,
            'stagnant_document',
            $title,
            $message,
            $document,
            $link,
            $severity
        );
    }

    /**
     * Crée une notification pour les documents à risque de retard
     */
    public function createRiskyDocumentNotification(Document $document, float $riskScore): void
    {
        $superviseur = $document->getSuperviseur();
        
        if (!$superviseur) {
            return;
        }
        
        $title = 'Risque de retard';
        $message = sprintf(
            'Le document %s (Ref: %s) présente un risque de retard (score: %d%%).',
            $document->getRefTitleFra() ?? 'sans titre',
            $document->getReference(),
            round($riskScore * 100)
        );
        
        $link = '/document/' . $document->getId();
        
        $severity = $riskScore > 0.7 ? 'danger' : ($riskScore > 0.4 ? 'warning' : 'info');
        
        $this->createNotification(
            $superviseur,
            'risky_document',
            $title,
            $message,
            $document,
            $link,
            $severity
        );
    }
}
