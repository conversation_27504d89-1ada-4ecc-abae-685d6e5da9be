<?php

namespace App\Repository;

use App\Entity\Document;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends ServiceEntityRepository<Document>
 */
class DocumentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Document::class);
    }

//    /**
//     * @return Document[] Returns an array of Document objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Document
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

// findByCurrentStep(string $step)

// public function findByCurrentStepNative(string $key): array
// {
//     // dd($key);
//     $conn = $this->getEntityManager()->getConnection();

//     // Construct the JSON path dynamically
//     $path = '$."' . $key . '"';

//     $sql = "SELECT *
//             FROM document d
//             WHERE JSON_EXTRACT(d.current_steps, :jsonPath) IS NOT NULL";

//     $stmt = $conn->prepare($sql);
//     $stmt->bindValue('jsonPath', $path);
//     $resultSet = $stmt->executeQuery();

//     return $resultSet->fetchAllAssociative();
// }

public function findByCurrentStepNative(string $position)
{
    // #[ORM\Column(type: 'json')]
    // private array $currentSteps = [];
    $documents = $this->findAll();
    $filteredDocuments = [];

    foreach ($documents as $document) {
        $currentSteps = $document->getCurrentSteps();
        foreach ($currentSteps as $step=>$key) {
            if (strpos($step, $position) !== false) {
                $filteredDocuments[] = $document;
                break;
            }
        }
    }

    return $filteredDocuments;
}

    public function findLatestByReference(string $reference): ?Document
    {
        // Utiliser une requête DQL plus explicite pour récupérer toutes les propriétés
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
            ->andWhere('d.reference = :reference')
            ->setParameter('reference', $reference)
            ->orderBy('d.refRev', 'DESC')
            ->setMaxResults(1);

        $query = $qb->getQuery();
        $result = $query->getOneOrNullResult();

        if ($result) {
            // Log pour déboguer
            error_log('Document trouvé dans le repository : ' . $result->getReference() . ' - ' . $result->getRefRev());
            error_log('Material : ' . ($result->getMaterial() ?? 'null'));
            error_log('Ex : ' . ($result->getEx() ?? 'null'));
            error_log('MaterialType : ' . ($result->getMaterialType() ?? 'null'));
            error_log('Action : ' . ($result->getAction() ?? 'null'));
            error_log('HTS : ' . ($result->getHts() ?? 'null'));
            error_log('ECCN : ' . ($result->getEccn() ?? 'null'));
            error_log('RDO : ' . ($result->getRdo() ?? 'null'));
            error_log('CustDrawing : ' . ($result->getCustDrawing() ?? 'null'));
        }

        return $result;
    }

    /**
     * Trouve des documents similaires en fonction du type de document, du type de processus et du type de matériau
     */
    public function findSimilarDocuments(?string $docType, ?string $procType, ?string $materialType, int $limit = 20): array
    {
        $qb = $this->createQueryBuilder('d');

        if ($docType) {
            $qb->andWhere('d.docType = :docType')
               ->setParameter('docType', $docType);
        }

        if ($procType) {
            $qb->andWhere('d.procType = :procType')
               ->setParameter('procType', $procType);
        }

        if ($materialType) {
            $qb->andWhere('d.Material_Type = :materialType')
               ->setParameter('materialType', $materialType);
        }

        return $qb->setMaxResults($limit)
                 ->getQuery()
                 ->getResult();
    }

    /**
     * Trouve des documents par période
     */
    public function findByPeriod(\DateTime $startDate, \DateTime $endDate): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            $firstDate = null;

            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        $enterDate = new \DateTime($entry['enter']);
                        if ($firstDate === null || $enterDate < $firstDate) {
                            $firstDate = $enterDate;
                        }
                    }
                } elseif (is_string($entries)) {
                    // Ancien format
                    $date = new \DateTime($entries);
                    if ($firstDate === null || $date < $firstDate) {
                        $firstDate = $date;
                    }
                }
            }

            if ($firstDate && $firstDate >= $startDate && $firstDate <= $endDate) {
                $result[] = $document;
            }
        }

        return $result;
    }

    /**
     * Trouve les documents à valider par un utilisateur
     */
    public function findDocumentsToValidateByUser($user): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            $visas = $document->getVisas();

            if (!$visas) {
                continue;
            }

            foreach ($visas as $visa) {
                if ($visa->getValidator() && $visa->getValidator()->getId() === $user->getId() && !$visa->getDateVisa()) {
                    $result[] = $document;
                    break;
                }
            }
        }

        return $result;
    }

    /**
     * Trouve les documents à réviser par un utilisateur
     */
    public function findDocumentsToReviewByUser($user): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            // Vérifier si l'utilisateur est le superviseur du document
            $isOwner = $document->getSuperviseur() && $document->getSuperviseur()->getId() === $user->getId();

            if (!$isOwner) {
                continue;
            }

            // Vérifier si le document a été rejeté récemment
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            $wasRejected = false;

            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['from_state']) && strpos(strtolower($entry['from_state']), 'reject') !== false) {
                            $wasRejected = true;
                            break;
                        }
                    }
                }

                if ($wasRejected) {
                    break;
                }
            }

            if ($wasRejected) {
                $result[] = $document;
            }
        }

        return $result;
    }

    /**
     * Trouve les documents traités entre deux dates
     */
    public function findDocumentsProcessedBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        $documents = $this->findAll();
        $result = [];

        foreach ($documents as $document) {
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            $lastDate = null;

            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['exit']) && $entry['exit'] !== null) {
                            $exitDate = new \DateTime($entry['exit']);
                            if ($lastDate === null || $exitDate > $lastDate) {
                                $lastDate = $exitDate;
                            }
                        }
                    }
                }
            }

            if ($lastDate && $lastDate >= $startDate && $lastDate <= $endDate) {
                $result[] = $document;
            }
        }

        return $result;
    }
}
