<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;

class DepartmentPerformanceService
{
    private EntityManagerInterface $entityManager;
    private DocumentRepository $documentRepository;
    private UserRepository $userRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        DocumentRepository $documentRepository,
        UserRepository $userRepository
    ) {
        $this->entityManager = $entityManager;
        $this->documentRepository = $documentRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Récupère les performances par département pour le tableau de bord
     */
    public function getDepartmentPerformance(): array
    {
        // Récupérer tous les départements
        $departments = $this->getDepartments();

        // Récupérer tous les documents
        $documents = $this->documentRepository->findAll();

        $departmentData = [
            'departments' => []
        ];

        foreach ($departments as $department) {
            // Filtrer les documents par département
            $departmentDocuments = array_filter($documents, function($document) use ($department) {
                $superviseur = $document->getSuperviseur();
                return $superviseur && $superviseur->getDepartement() === $department;
            });

            // Calculer les métriques de performance
            $processingTime = $this->calculateAverageProcessingTime($departmentDocuments);
            $documentCount = count($departmentDocuments);
            $rejectionRate = $this->calculateRejectionRate($departmentDocuments);

            $departmentData['departments'][] = [
                'name' => $department,
                'processing_time' => $processingTime,
                'document_count' => $documentCount,
                'rejection_rate' => $rejectionRate
            ];
        }

        // Trier par temps de traitement (du plus court au plus long)
        usort($departmentData['departments'], function($a, $b) {
            return $a['processing_time'] <=> $b['processing_time'];
        });

        return $departmentData;
    }

    /**
     * Calcule le temps moyen de traitement des documents
     */
    private function calculateAverageProcessingTime(array $documents): float
    {
        if (empty($documents)) {
            return 0;
        }

        $totalTime = 0;
        $count = 0;

        foreach ($documents as $document) {
            $processingTime = $this->getDocumentProcessingTime($document);
            if ($processingTime > 0) {
                $totalTime += $processingTime;
                $count++;
            }
        }

        return $count > 0 ? round($totalTime / $count, 1) : 0;
    }

    /**
     * Calcule le temps de traitement d'un document
     */
    private function getDocumentProcessingTime($document): float
    {
        $timestamps = $document->getRawStateTimestamps();
        if (!$timestamps) {
            return 0;
        }

        $totalDays = 0;

        foreach ($timestamps as $state => $entries) {
            $stateDays = $document->getTotalDaysInState($state);
            if ($stateDays !== null) {
                $totalDays += $stateDays;
            }
        }

        return $totalDays;
    }

    /**
     * Calcule le taux de rejet des documents
     */
    private function calculateRejectionRate(array $documents): float
    {
        if (empty($documents)) {
            return 0;
        }

        $rejectedCount = 0;

        foreach ($documents as $document) {
            if ($this->hasBeenRejected($document)) {
                $rejectedCount++;
            }
        }

        return round(($rejectedCount / count($documents)) * 100, 1);
    }

    /**
     * Vérifie si un document a été rejeté au moins une fois
     */
    private function hasBeenRejected($document): bool
    {
        $timestamps = $document->getRawStateTimestamps();
        if (!$timestamps) {
            return false;
        }

        foreach ($timestamps as $state => $entries) {
            if (is_array($entries)) {
                foreach ($entries as $entry) {
                    if (isset($entry['from_state']) && strpos(strtolower($entry['from_state']), 'reject') !== false) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Récupère les performances par département (version détaillée)
     */
    public function getDepartmentPerformanceDetailed(string $period = 'month', int $limit = 6): array
    {
        $departments = $this->getDepartments();
        $now = new \DateTime();
        $startDate = clone $now;

        // Définir la période d'analyse
        switch ($period) {
            case 'week':
                $startDate->modify('-' . $limit . ' weeks');
                $interval = 'week';
                $format = 'W/Y';
                break;
            case 'month':
                $startDate->modify('-' . $limit . ' months');
                $interval = 'month';
                $format = 'm/Y';
                break;
            case 'quarter':
                $startDate->modify('-' . ($limit * 3) . ' months');
                $interval = 'quarter';
                $format = 'Q/Y';
                break;
            case 'year':
                $startDate->modify('-' . $limit . ' years');
                $interval = 'year';
                $format = 'Y';
                break;
            default:
                $startDate->modify('-' . $limit . ' months');
                $interval = 'month';
                $format = 'm/Y';
        }

        // Récupérer tous les documents traités dans la période
        $documents = $this->documentRepository->findDocumentsProcessedBetween($startDate, $now);

        // Initialiser les données de performance
        $performance = [
            'processing_time' => [],
            'document_count' => [],
            'rejection_rate' => [],
            'periods' => $this->generatePeriods($now, $limit, $interval, $format)
        ];

        // Initialiser les données pour chaque département
        foreach ($departments as $department) {
            $performance['processing_time'][$department] = [];
            $performance['document_count'][$department] = [];
            $performance['rejection_rate'][$department] = [];

            foreach ($performance['periods'] as $periodKey => $periodData) {
                $performance['processing_time'][$department][$periodKey] = 0;
                $performance['document_count'][$department][$periodKey] = 0;
                $performance['rejection_rate'][$department][$periodKey] = 0;
            }
        }

        // Analyser les documents
        foreach ($documents as $document) {
            $processingData = $this->calculateDocumentProcessingData($document);
            if (!$processingData) {
                continue;
            }

            $department = $processingData['department'] ?? 'Inconnu';
            $processingTime = $processingData['processing_time'];
            $wasRejected = $processingData['was_rejected'];
            $periodKey = $this->getPeriodKey($processingData['completion_date'], $interval, $format);

            // Vérifier si la période existe dans nos données
            if (!isset($performance['periods'][$periodKey])) {
                continue;
            }

            // Ajouter les données de ce document
            if (!isset($performance['processing_time'][$department][$periodKey])) {
                $performance['processing_time'][$department][$periodKey] = 0;
                $performance['document_count'][$department][$periodKey] = 0;
                $performance['rejection_rate'][$department][$periodKey] = 0;
            }

            $performance['processing_time'][$department][$periodKey] += $processingTime;
            $performance['document_count'][$department][$periodKey]++;

            if ($wasRejected) {
                $performance['rejection_rate'][$department][$periodKey]++;
            }
        }

        // Calculer les moyennes et les taux
        foreach ($departments as $department) {
            foreach ($performance['periods'] as $periodKey => $periodData) {
                if ($performance['document_count'][$department][$periodKey] > 0) {
                    // Temps moyen de traitement
                    $performance['processing_time'][$department][$periodKey] = round(
                        $performance['processing_time'][$department][$periodKey] / $performance['document_count'][$department][$periodKey],
                        1
                    );

                    // Taux de rejet
                    $performance['rejection_rate'][$department][$periodKey] = round(
                        ($performance['rejection_rate'][$department][$periodKey] / $performance['document_count'][$department][$periodKey]) * 100,
                        1
                    );
                }
            }
        }

        return $performance;
    }

    // Renommer l'ancienne méthode pour éviter les conflits

    /**
     * Récupère la liste des départements
     */
    private function getDepartments(): array
    {
        $departments = [];
        $users = $this->userRepository->findAll();

        foreach ($users as $user) {
            $department = $user->getDepartement();
            if ($department && !in_array($department, $departments)) {
                $departments[] = $department;
            }
        }

        return $departments;
    }

    /**
     * Génère les périodes pour l'analyse
     */
    private function generatePeriods(\DateTime $endDate, int $limit, string $interval, string $format): array
    {
        $periods = [];
        $date = clone $endDate;

        for ($i = 0; $i < $limit; $i++) {
            if ($i > 0) {
                switch ($interval) {
                    case 'week':
                        $date->modify('-1 week');
                        break;
                    case 'month':
                        $date->modify('-1 month');
                        break;
                    case 'quarter':
                        $date->modify('-3 months');
                        break;
                    case 'year':
                        $date->modify('-1 year');
                        break;
                }
            }

            $periodKey = $date->format($format);

            $periods[$periodKey] = [
                'start_date' => clone $date,
                'label' => $periodKey
            ];
        }

        // Inverser pour avoir les périodes dans l'ordre chronologique
        return array_reverse($periods, true);
    }

    /**
     * Calcule les données de traitement d'un document
     */
    private function calculateDocumentProcessingData($document): ?array
    {
        $timestamps = $document->getRawStateTimestamps();
        if (!$timestamps) {
            return null;
        }

        // Trouver la première et la dernière date
        $firstDate = null;
        $lastDate = null;
        $wasRejected = false;

        foreach ($timestamps as $state => $entries) {
            if (is_array($entries)) {
                foreach ($entries as $entry) {
                    if (isset($entry['enter'])) {
                        $enterDate = new \DateTime($entry['enter']);
                        if ($firstDate === null || $enterDate < $firstDate) {
                            $firstDate = $enterDate;
                        }
                    }

                    if (isset($entry['exit']) && $entry['exit'] !== null) {
                        $exitDate = new \DateTime($entry['exit']);
                        if ($lastDate === null || $exitDate > $lastDate) {
                            $lastDate = $exitDate;
                        }
                    }

                    // Vérifier si le document a été rejeté
                    if (isset($entry['from_state']) && strpos(strtolower($entry['from_state']), 'reject') !== false) {
                        $wasRejected = true;
                    }
                }
            }
        }

        if (!$firstDate || !$lastDate) {
            return null;
        }

        // Déterminer le département responsable
        $department = 'Inconnu';
        $user = $document->getSuperviseur();
        if ($user && $user->getDepartement()) {
            $department = $user->getDepartement();
        }

        $diff = $lastDate->diff($firstDate);
        $processingTime = $diff->days;

        return [
            'department' => $department,
            'processing_time' => $processingTime,
            'was_rejected' => $wasRejected,
            'completion_date' => $lastDate
        ];
    }

    /**
     * Obtient la clé de période pour une date donnée
     */
    private function getPeriodKey(\DateTime $date, string $interval, string $format): string
    {
        return $date->format($format);
    }
}
