{% extends 'base.html.twig' %}

{% block title %}Documents{% endblock %}

{% block body %}
<style>
    /* Vos styles existants */
    #table-container {
        padding: 1rem;
        background-color: #F8F9FA;
        border-radius: 0.5rem;
    }
    #table tr:last-child {
        border: none!important;
    }
    .table-filter-input {
        width: 100%;
        font-size: 0.75rem;
        height: calc(1.8rem + 2px);
    }
    #table td span {
        cursor: pointer;
        transition: all 0.3s;
    }
    #table td span:hover {
        padding: 0.375rem 0.75rem;
    }
    #table th, #table td {
        vertical-align: middle!important;
        white-space: nowrap;
        text-align: center!important;
        padding: 0.15rem!important;
        border: none!important;
    }
    #table thead th {
        user-select: none;
    }
    #table thead tr {
        border: none;
    }
    #table thead tr#entetes th {
        background-color: #004080;
        color: #fff;
        font-size: 0.75rem;
    }
    td{
        font-size: 0.72rem!important;
    }
    #table thead tr#filtres th {
        background-color: #F8F9FA;
        border: none;
        cursor: pointer;
    }
    /* Icônes de tri */
    th.sort-asc i, th.sort-desc i {
        margin-left: 5px;
    }
    /* Badges */
    .badge.bg-primary {
        background: #0059B3!important;
    }
    /* Champs invalides */
    .is-invalid {
        border-color: #dc3545;
    }
    /* Boutons */
    .btn-refresh {
        margin-bottom: 1rem;
    }
    /* --- Modale personnalisée --- */
    .modal-dialog.modal-lg {
        max-width: 900px;
    }
    .modal-content.custom-modal-content {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    .modal-header.custom-modal-header {
        background-color: #004080;
        color: #fff;
        border-bottom: none;
    }
    .modal-header.custom-modal-header .btn-close {
        filter: invert(100%);
    }
    .modal-footer.custom-modal-footer {
        border-top: none;
    }
    .tooltip .tooltip-inner {
        max-width: 400px;
        overflow-y: auto;
        white-space: normal;
    }

    .item {
        padding: 0.5rem 1rem;
    }

    .header {
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    label {
        font-size: 0.85rem;
        margin-bottom: 0.03rem!important;
    }

    p {
        margin-bottom: 0;
    }

    .fields-gid{
        display: flex;
        align-items: center;
        column-gap: 15px;
    }

    .fields-gid>*{
        white-space: nowrap;
    }

    textarea::placeholder {
        font-size: 0.75rem;
    }

    .f1 {
        min-width: 4rem;
    }

    @media (min-width: 1600px) {
        .custom-col {
            flex: 0 0 auto;
            width: 33.3333%; /* équivalent à col-4 */
        }

        .custom-2 {
            flex: 0 0 auto;
            width: 33.3333%; /* équivalent à col-4 */
        }
        
        .custom-3 {
            flex: 0 0 auto;
            width: 66.6667%; /* équivalent à col-8 */
        }
    }


/* Styles pour la pagination */
.pagination-container {
    margin: 20px 0;
}

.pagination-container .pagination {
    margin-bottom: 0;
}

.pagination-container .page-item.active .page-link {
    background-color: #004080;
    border-color: #004080;
}

.pagination-container .page-link {
    color: #004080;
}

.pagination-container .page-link:hover {
    color: #002040;
    background-color: #e9ecef;
}

</style>

<div class="mt-3" style="margin: 0 1%">
    <div class="row mb-2">
        <!-- Partie gauche : le tableau -->
        <div class="col-5 custom-2">
            <h4 class="mb-2">SAP Core Data</h4>
            {% if documents is empty %}
                <div class="alert alert-warning" role="alert">
                    Aucun document trouvé.
                </div>
            {% else %}
                <div class="card shadow border-0">
                    <div class="card-body p-0">
                        <div id="table-container" class="p-1">
                            <table class="table table-hover table-bordered mb-0" id="table" style="user-select: none;">
                                <thead>
                                    <!-- Ligne de filtres -->
                                    <tr id="filtres">
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Pack" data-col="0"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Project" data-col="1"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Action" data-col="2"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Référence" data-col="3"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Plan" data-col="4"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Type" data-col="5"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Proc" data-col="6"></th>
                                        <th>
                                            <span class="badge bg-secondary " style="font-size: 0.70rem;">
                                                {{ documents|length }}{{ documents|length == 1 ? ' Document' : ' Documents' }}
                                            </span>
                                        </th>
                                    </tr>
                                    <!-- Ligne d'entêtes -->
                                    <tr id="entetes">
                                        <th>Pack</th>
                                        <th>Project</th>
                                        <th>Action</th>
                                        <th>Référence</th>
                                        <th>Plan</th>
                                        <th>Type</th>
                                        <th>Proc</th>
                                        <th>Visas</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for document in documents %}
                                        <tr document-id="{{document.id}}" doc-id="{{ document.id }}" class="document-row">
                                            <td><a href="{{ path('detail_package', {'id': document.relPack.id}) }}" class="badge bg-primary pack-link">{{document.relPack.id}}</a></td>
                                            <td>{{ document.relPack.getProjectRelation }}</td>
                                            {% if document.action == 'Création' %}
                                                <td style="color: blue;font-weight: bold">
                                                    {{ document.action }}
                                                </td>
                                            {% else %}
                                                <td>
                                                    {{ document.action }}
                                                </td>
                                            {% endif %}
                                            </td>
                                            <td>{{ document.reference }}</td>
                                            <td>{{ document.prodDraw }}</td>
                                            <td>{{ document.docType }}</td>
                                            <td>{{ document.procType }}</td>
                                            <td>
                                                {% if document.hasGIDCommentaire %}
                                                    <span class="badge bg-danger">
                                                        <i class="fa-solid fa-flag"></i>
                                                    </span>
                                                {% endif %}
                                                <span class="badge bg-secondary" onclick="showVisas({{ document.id }})">
                                                    <i class="fa-solid fa-passport"></i>
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div><!-- ./table-container -->
                    </div><!-- ./card-body -->
                </div><!-- ./card -->
                {# Pagination supprimée #}
            {% endif %}
        </div><!-- ./col (table) -->

        <!-- Partie droite : formulaire reconfiguré en accordéon -->
        <div class="col-7 custom-3" id="form-container">
            <div class="card shadow border-0">
                <div class="card-body p-1" id="form-core-data" style="display: none">
                    <input type="hidden" class="item" id="documentId" value="">
                    <div>
                        <!-- 1. Données de base 1 -->
                        <div class="item">
                            <h6 id="headingBase1" class="header d-flex justify-content-between align-items-center">
                                Données de base 1
                                <span class="badge bg-primary">Projet <span id="project" class="mb-0"></span></span>
                            </h6>
                            <div class="row">
                                <div class="fields-gid col-6 custom-col">
                                    <label for="reference" class="form-label f1 fw-bold">Référence</label>
                                    <div class="d-flex align-items-center">
                                        <input 
                                            type="text" 
                                            id="reference" 
                                            class="form-control form-control-sm reference-input" 
                                            value="" 
                                            doc-id="" 
                                            style="max-width: 10rem!important"
                                        >
                                        <span id="refRev" class="badge bg-primary ms-2">Rev</span>
                                    </div>
                                </div>
                                <div class="fields-gid col-6 custom-col">
                                    <label for="commodityCode-val" class="form-label f1 fw-bold">Grp. Articles</label>
                                    <p id="commodityCode-val" class="mb-0"></p>
                                </div>
                                
                                <div class="fields-gid col-6 custom-col">
                                    <label for="productCode-val" class="form-label f1 fw-bold">Hiér. produits</label>
                                    <p id="productCode-val" class="mb-0"></p>
                                </div>
                                <div class="fields-gid col-6 custom-col">
                                    <label for="Unit" class="form-label f1 fw-bold">Unit</label>
                                    <p id="Unit" class="mb-0"></p>
                                </div>
                                <div class="fields-gid col-6 custom-col">
                                    <label for="rdo" class="form-label f1 fw-bold">Grp. Autorisation</label>
                                    <p id="rdo" class="mb-0"></p>
                                </div>
                                <div class="fields-gid col-6 custom-col">
                                    <label for="weight" class="form-label f1 fw-bold">Masse</label>
                                    <p class="mb-0">
                                        <span id="weight" class="mb-0"></span>
                                        <span id="weightUnit" class="ms-1 mb-0"></span>
                                    </p>
                                </div>
                                <div class="fields-gid col-6 custom-col">
                                    <label for="refTitle" class="form-label f1 fw-bold">Titre</label>
                                    <p id="refTitle" class="mb-0"></p>
                                </div>
                                <div class="fields-gid col-6 custom-col">
                                    <label for="matProdType" class="form-label f1 fw-bold">Type Article</label>
                                    <p id="materialType" class="mb-0"></p>
                                    <p id="matProdType" class="mb-0"></p>
                                </div>
                            </div>
                        </div>

                        <!-- 2. Données de base 2 -->
                        <div class="item">
                            <h6 class="header" id="headingBase2">
                                Données de base 2
                            </h6>
                            <div>
                                <div class="row">
                                    <div class="fields-gid col-6">
                                        <label for="prodDraw" class="form-label fw-bold">Document </label>
                                        <div class="d-flex align-items-center">
                                            <p id="prodDraw" class="mb-0"></p>
                                            <span id="prodDrawRev" class="badge bg-primary ms-2">Rev</span>
                                        </div>
                                    </div>
                                    <div class="fields-gid col-6">
                                        <label for="alias" class="form-label fw-bold">Alias</label>
                                        <p id="alias" class="mb-0"></p>
                                    </div>
                                    <!-- S’il n'y a pas de 4e champ ici, on laisse la 4e colonne vide ou on omet la col-3 -->
                                </div>
                            </div>
                        </div>

                        <!-- 3. Classification -->
                        <div class="item">
                            <h6 class="header" id="headingClassification">
                                Classification
                            </h6>
                            <div>
                                <div class="row">
                                    <div class="fields-gid col-3">
                                        <label for="eccn-val" class="form-label fw-bold">ECCN</label>
                                        <p id="eccn-val" class="mb-0"></p>
                                    </div>
                                    <div class="fields-gid col-9">
                                        <label for="exigenceDocumentaire" class="form-label fw-bold">
                                            Exigences Doc.
                                            <span id="docImpactIcon">
                                                <i class="fa-solid fa-triangle-exclamation fa-bounce invisible" style="color: #ff0000;"></i>
                                            </span>
                                        </label>
                                        <select id="exigenceDocumentaire" class="selectpicker qDocRec-multiple" multiple data-selected-text-format="count > 10" data-style="btn-white btn-sm border" data-width="100%" data-live-search="true" title="Sélectionner une exigence documentaire">
                                            <option value="Z01">Z01</option>
                                            <option value="Z02">Z02</option>
                                            <option value="Z03">Z03</option>
                                            <option value="Z04">Z04</option>
                                            <option value="Z05">Z05</option>
                                            <option value="Z06">Z06</option>
                                            <option value="Z07">Z07</option>
                                            <option value="Z08">Z08</option>
                                            <option value="Z09">Z09</option>
                                            <option value="Z10">Z10</option>
                                            <option value="Z11">Z11</option>
                                            <option value="Z12">Z12</option>
                                            <option value="Z13">Z13</option>
                                            <option value="Z14">Z14</option>
                                            <option value="Z15">Z15</option>
                                            <option value="Z16">Z16</option>
                                            <option value="Z17">Z17</option>
                                            <option value="Z18">Z18</option>
                                            <option value="Z19">Z19</option>
                                            <option value="Z20">Z20</option>
                                            <option value="Z21">Z21</option>
                                            <option value="Z22">Z22</option>
                                            <option value="Z23">Z23</option>
                                            <option value="Z24">Z24</option>
                                            <option value="Z25">Z25</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 7. ADV données générales/div -->
                        <div class="row mx-0">
                            <!-- Bloc ADV -->
                            <div class="item col">
                                <h6 class="header" id="headingAdvGeneral">
                                    ADV données générales/div
                                </h6>
                                <div>
                                    <div class="row">
                                        <div class="fields-gid col-6">
                                            <label for="grpeArtEmbExp" class="form-label fw-bold">Grpe art. Emb.expéd.</label>
                                            <p id="grpeArtEmbExp" class="mb-0"></p>
                                        </div>
                                        <div class="fields-gid col-6">
                                            <label for="typeEmballageExp" class="form-label fw-bold">Type emballage exp.</label>
                                            <p id="typeEmballageExp" class="mb-0"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 8. Planif des besoins 2 -->
                            <div class="item col">
                                <h6 class="header" id="headingPlanif">
                                    Planif des besoins 2
                                </h6>
                                <div>
                                    <div class="row">
                                        <div class="fields-gid col-6">
                                            <label for="procType" class="form-label fw-bold">Type appro</label>
                                            <p id="procType"></p>
                                        </div>
                                        <div class="fields-gid col-6">
                                            <label for="ex" class="form-label fw-bold">EX</label>
                                            <p id="ex"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 9. Management qualité -->
                        <div class="item">
                            <h6 class="header" id="headingManagement">
                                Management qualité
                            </h6>
                            <div>
                                <div class="row">
                                    <div class="fields-gid col-3">
                                        <label for="inspect" class="form-label fw-bold">Inspect</label>
                                        <select 
                                            id="inspect" 
                                            class="selectpicker inspect-select" 
                                            multiple 
                                            data-style="btn-white btn-sm border" 
                                            data-width="100%" 
                                            data-live-search="true" 
                                            title="Sélectionner une inspect"
                                        >
                                            <option value="Z01">Z01</option>
                                            <option value="Z04">Z04</option>
                                            <option value="Z08">Z08</option>
                                        </select>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="qDynamization" class="form-label fw-bold">Régle de Dynamisation</label>
                                        <p id="qDynamization"></p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="qControlRouting" class="form-label fw-bold">Gamme de contrôle</label>
                                        <p id="qControlRouting"></p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="tpsRecept" class="form-label fw-bold">Tps Recept</label>
                                        <p id="tpsRecept" class="mb-0"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 10. Autres informations Diffusion -->
                        <div class="item">
                            <h6 class="header" id="headingDiffusion">
                                Autres informations Diffusion
                            </h6>
                            <div>
                                <!-- Ici on a beaucoup de champs, on découpe en plusieurs rows de 4 col -->
                                <div class="row">
                                    <div class="fields-gid col-3">
                                        <label for="relPackNum" class="form-label fw-bold">Diffusion</label>
                                        <p id="relPackNum"></p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="activity" class="form-label fw-bold">Activité</label>
                                        <p id="activity"></p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="custDrawing" class="form-label fw-bold">Cust Drawing</label>
                                        <p>
                                            <span id="custDrawing"></span>
                                            <span id="custDrawingRev"></span>
                                        </p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="action" class="form-label fw-bold">Action</label>
                                        <p id="action"></p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="fields-gid col-3">
                                        <label for="inventoryImpact" class="form-label fw-bold">Impact En-cours</label>
                                        <p id="inventoryImpact"></p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="prisDans1-val" class="form-label fw-bold">Pris Dans 1</label>
                                        <p id="prisDans1-val"></p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="prisDans2-val" class="form-label fw-bold">Pris Dans 2</label>
                                        <p id="prisDans2-val"></p>
                                    </div>
                                    <div class="fields-gid col-3">
                                        <label for="internalMachRec" class="form-label fw-bold">Fab. SCM Reco.</label>
                                        <p id="internalMachRec"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 11. Bloc Commentaires -->
                        <div class="item">
                            <h6 class="header" id="headingCommentaires">
                                Bloc Commentaires
                            </h6>
                            <div>
                                <div class="row">
                                    <div class="col-4">
                                        <label for="requestorComments" class="form-label fw-bold">Commentaire Créateur (BE)</label>
                                        <textarea disabled id="requestorComments" class="form-control" rows="2"></textarea>
                                    </div>
                                    <div class="col-4">
                                        <label for="relPackDescription" class="form-label fw-bold">Observations Diffusion</label>
                                        <textarea disabled id="relPackDescription" class="form-control" rows="2"></textarea>
                                    </div>
                                    <div class="col-4">
                                        <label for="generalComments" class="form-label fw-bold">Commentaires Généraux</label>
                                        <textarea disabled id="generalComments" class="form-control" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 12. Bloc final : actions et Aletiq -->
                        <div class="item d-flex justify-content-between align-items-center">
                            <div class="d-flex flex-column" style="width: 50%">
                                <label for="gid_comment" class="form-label fw-bold">GID Commentaire</label>
                                <textarea id="gid_comment" comment-id="" class="form-control" rows="2" ></textarea>
                            </div>
                            <div>
                                <a id="idaletiq" class="badge bg-primary" href="" target="_blank">Aletiq</a>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <label for="switchAletiq" class="form-check-label">Brouillon</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input switchAletiq-checkbox" type="checkbox" id="switchAletiq" doc-id="">
                                </div>
                                <label for="switchAletiq" class="form-check-label">Validé</label>
                            </div>
                            <div class="text-end">
                                <span type="button" id="signature-gid" class="badge bg-primary">Signer</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalVisas" tabindex="-1" aria-labelledby="modalVisasLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalVisasLabel">Historique des visas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>

<script>
const RDO_text = {
    '0578': 'SCM_ENERGY',
    '1229': 'SCM_ENERGY_Sensitive',
    '1226': 'SCM_MOB_INDUS_Sensitive',
    '579': 'SCM_MOB_INDUS',
    '580': 'SCM_MOB_AERO',
    '581': 'SCM_MOB_AERO_Sensitive',
    '825': 'SCM_MATERIAL',
    '826': 'SCM_TOOLING'
};

const productCode_text = {
    'Y23B': 'SCM',
    'X992': 'CKB Mil/Aero',
    'W6C4': 'SEA-VI 6-100',
    'W33J': 'SEA-VI 33-400',
    'W33A': 'SEA-VI 33-900',
    'W132': 'SEA-VI 132-XXXX',
    'W11T': 'SEA-VI 11-250',
    'W11P': 'SEA-VI 11-400',
    'W11G': 'SEA-VI 11-1600',
    'P11K': 'P11-PS500 & P11-PS650',
    'MSDX': 'MSD',
    'MPDX': 'MPD',
    'MODX': 'MOD',
    'ILCO': 'ILC',
    'ETHO': 'ETH/EHTF-optic',
    'ETHE': 'ETH/EHTF-electric',
    'EFSA': 'EFS',
    'DW66': 'SEA-VI 66-1250',
    'CCNO': 'CC-NODE',
    'AMBP': 'AMB penetrators',
    '9316': '9316 (9416 etc..)'
};

$(document).on('click', '.document-row', function() {
    $('.document-row').removeClass('table-active');
    $(this).addClass('table-active');
    fait($(this).attr('document-id'));
});

function fait(documentId) {
    var url = "{{ path('get_document', {'id': 0}) }}";
    url = url.replace('0', documentId);

    // Si on clique sur une badge ou si le document sélectionné est déjà actif, on quitte
    if ($(event.target).hasClass('badge')) {
        return;
    }

    $('#table tbody tr').removeClass('table-active');
    // $(this).addClass('table-active');

    $.ajax({
        url: url,
        type: 'POST',
        data: {
            documentId: documentId
        },
        async: true,
        success: function(response) {
            let doc = response;
            console.log(doc);
            $('#form-core-data').slideDown();
            // Remplissage des champs de saisie et selectpickers déjà gérés
            $('#reference').val(doc.reference);
            $('#reference').attr('doc-id', doc.id);
            $('#documentId').val(doc.id || "");

            // --- Selectpicker "Exigence Documentaire" ---
            var $selectEl = $('#exigenceDocumentaire');
            $selectEl.selectpicker('destroy');
            $selectEl.attr('doc-id', doc.id);
            $selectEl.find('option').prop('selected', false);
            if (doc.qDocRec && Array.isArray(doc.qDocRec)) {
                doc.qDocRec.forEach(function(val) {
                    $selectEl.find('option[value="' + val + '"]').prop('selected', true);
                });
            }
            if (typeof $selectEl.selectpicker === 'function') {
                $selectEl.selectpicker();
            }

            // --- Selectpicker "Inspect" ---
            var $inspectEl = $('#inspect');
            $inspectEl.selectpicker('destroy');
            $inspectEl.attr('doc-id', doc.id);
            $inspectEl.find('option').prop('selected', false);
            if (doc.qInspection && Array.isArray(doc.qInspection)) {
                doc.qInspection.forEach(function(val) {
                    $inspectEl.find('option[value="' + val + '"]').prop('selected', true);
                });
            }
            if (typeof $inspectEl.selectpicker === 'function') {
                $inspectEl.selectpicker();
            }

            // --- Textarea Commentaire ---
            var commentPlaceholder = "";
            if (doc.commentaires && Array.isArray(doc.commentaires)) {
                commentPlaceholder = doc.commentaires.join("\n");
            }
            $('#generalComments').attr('placeholder', commentPlaceholder);

            $('#relPackDescription').attr('placeholder', doc.relPackDescription);

            var commentRequestor = "";
            if (doc.commentaires_By_Owner && Array.isArray(doc.commentaires_By_Owner)) {
                commentRequestor = doc.commentaires_By_Owner.join("\n");
            }
            $('#requestorComments').attr('placeholder', commentRequestor);

            // Mise à jour des balises <p> et des inputs du formulaire
            $('#refRev').text("Rev " + doc.refRev || "");
            $('#commodityCode-val').text(doc.commodityCode || "");
            $('#productCode-val').text((productCode_text[doc.productCode]) ? doc.productCode + ' - ' + productCode_text[doc.productCode] : doc.productCode);

            $('#weight').text(doc.Weight || "");

            $('#weightUnit').text(doc.WeightUnit || "");
            $('#project').text(doc.project || "");
            $('#refTitle').text(doc.refTitleFra || "");
            $('#materialType').text(doc.Material_Type || "");
            $('#matProdType').text(doc.matProdType || "");
            $('#drawingPath').text(doc.drawingPath || "");
            $('#rdo').text((RDO_text[doc.rdo]) ? doc.rdo + ' - ' + RDO_text[doc.rdo] : doc.rdo);

            $('#prodDraw').text(doc.prodDraw || "");
            $('#prodDrawRev').text("Rev " + doc.prodDrawRev || "");
            $('#alias').text(doc.alias || "");

            $('#eccn-val').text(doc.eccn || "");

            $('#Unit').text(doc.Unit || "");

            $('#procType').text(doc.procType || "");
            $('#ex').text(doc.ex || "");

            if (doc.GID) {
                $('#gid_comment').val(doc.GID.commentaire || "");
                $('#gid_comment').attr('comment-id', doc.GID.id || "");
            } else {
                $('#gid_comment').val("");
                $('#gid_comment').attr('comment-id', "");
            }

            $('#qDynamization').text(doc.qDynamization || "");
            $('#qControlRouting').text(doc.qControlRouting || "");
            $('#tpsRecept').text(doc.qControlRouting === 'Z001' ? '5 jours' : doc.qControlRouting === 'Z002' ? '20 jours' : "");

            $('#relPackNum').text(doc.relPack || "");
            $('#activity').text(doc.activity || "");
            $('#custDrawing').text(doc.CustDrawing || "");
            $('#custDrawingRev').text(doc.CustDrawingRev || "");
            $('#action').text(doc.action || "");
            $('#inventoryImpact').text(doc.inventory_impact || "");
            $('#prisDans1-val').text(doc.prisDans1 || "");
            $('#prisDans2-val').text(doc.prisDans2 || "");
            $('#internalMachRec').html(doc.internalMachRec ? '<img src="{{ asset('scm.png') }}" alt="scm" style="height: 20px;">' : "No");

            $('#grpeArtEmbExp').text(doc.matProdType === 'FERT' || doc.matProdType === 'HALB' || doc.matProdType === 'ROH' ? '0001' : doc.matProdType === 'VERP' ? '0002' : "");
            $('#typeEmballageExp').text(doc.matProdType === 'FERT' || doc.matProdType === 'HALB' || doc.matProdType === 'ROH' ? '' : doc.matProdType === 'VERP' ? '0004' : "");

            


            if (doc.idAletiq) {
                $('#idaletiq').attr('href', 'https://app.aletiq.com/products/' + doc.idAletiq + '/definition').attr('target', '_blank');
            } else {
                $('#idaletiq').attr('href', '');
            }
            

            $('#switchAletiq').prop('checked', doc.switchAletiq || false);
            $('#switchAletiq').attr('doc-id', doc.id);
        
            if (doc.docImpact) {
                $('#docImpactIcon').html('<i class="fa-solid fa-triangle-exclamation fa-bounce" style="color: #ff0000;"></i>');
            } else {
                $('#docImpactIcon').html('<i class="fa-solid fa-triangle-exclamation fa-bounce invisible" style="color: #ff0000;"></i>');
            }
        }
    });

    $('.form-label, .fields-gid>p').on('click', function() {
        var inputId = $(this).is('label') ? $(this).attr('for') : $(this).attr('id');
        var balise = $('#' + inputId);
        var inputVal = balise.is('p') ? balise.text() : balise.val();
        if (inputVal) {
            var tmp = $("<input>");
            $("body").append(tmp);
            tmp.val(inputVal).select();
            document.execCommand("copy");
            tmp.remove();
            Toast.fire({
                icon: 'success',
                title: 'Texte copié: ' + inputVal
            });
        }
    });

    $('.reference-input, .inspect-select, .qDocRec-multiple').on('change', function() {
        let activeRow = $('#table tbody tr.table-active').attr('doc-id');
        setTimeout(function() {
            $.ajax({
                url: "{{ path('app_document_place', {'place': 'Core_Data'}) }}",
                type: 'GET',
                success: function(response) {
                    $('#table-container').html($(response).find('#table'));
                    $('#table tbody tr[doc-id="' + activeRow + '"]').addClass('table-active');
                }
            });
        }, 3000);
    });
}

function commentGID(){
    var comment = $('#gid_comment').val();
    var commentId = $('#gid_comment').attr('comment-id');
    let documentId = $('#documentId').val();
    var url = "{{ path('comment_gid') }}";
    $.ajax({
        url: url,
        type: 'POST',
        data: {
            comment: comment,
            commentId: commentId,
            documentId: documentId,
            state: 'Core_Data'
        },
        async: true,
        success: function(response) {
            Toast.fire({
                icon: 'success',
                title: 'Commentaire enregistré'
            });
            fait($('#documentId').val());
        },
        error: function(response) {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors de l\'enregistrement du commentaire'
            });
        }
    });
}

$(document).on('change', '#gid_comment', commentGID);

$(document).on('click', '#signature-gid', function() {
    if ($('#switchAletiq').is(':checked')) {
        createVisa($('#documentId').val());
    }else{
        Toast.fire({
            icon: 'error',
            title: 'Veuillez valider le document sur Aletiq avant de signer'
        });
    }
});
</script>

{% include 'js/jhess.html.twig' %}
{% endblock %}

