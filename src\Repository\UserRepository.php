<?php

namespace App\Repository;

use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;
use Symfony\Bridge\Doctrine\Security\User\UserLoaderInterface;
use App\Service\LdapService;
// use App\Entity\Service;


/**
 * @extends ServiceEntityRepository<User>
 */
class UserRepository extends ServiceEntityRepository implements UserLoaderInterface
{

    private $ldapService;
    public function __construct(ManagerRegistry $registry, LdapService $ldapService)
    {
        parent::__construct($registry, User::class);
        $this->ldapService = $ldapService;
    }



    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', $user::class));
        }

        $user->setPassword($newHashedPassword);
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }

    public function loadUserByIdentifier(string $usernameOrEmail): ?User
    {
        // $this->initializeUsers();
        $entityManager = $this->getEntityManager();

        // Récupération du username depuis la session ou via SSO
        $username = explode("\\", $_SERVER['REMOTE_USER'])[1];

        if ($username == "srochdi"){
            $username = "fbourret";
            // $username = "acrapis";
        }

        // if($username =="eerdmann"){
        //     $username = "pjoubert";
        // }
        // Récupération des infos LDAP
        $ldapUserInfo = $this->ldapService->getUserInfoByUsernameInscription($username);
        $isInVpn = $this->ldapService->isUserInVpn($username, 'VPN_SSL', 'OU=FRSCM_Groupes,DC=scmlemans,DC=com');

        if (!$ldapUserInfo) {
            return null;
        }

        $nom = $ldapUserInfo['nom'] ?? null;
        $prenom = $ldapUserInfo['prenom'] ?? null;

        // Si "nom" n'est pas défini, essayons d'extraire ces informations depuis "distinguishedName"
        if (null === $nom && isset($ldapUserInfo['distinguishedName'])) {
            if (preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches)) {
                $parts = explode(' ', $matches[1], 2);
                $nom = $parts[0] ?? 'Nom Inconnu';
                $prenom = $parts[1] ?? 'Prénom Inconnu';
            } else {
                $nom = 'Nom Inconnu';
                $prenom = 'Prénom Inconnu';
            }
        }

        // Vérifier si l'utilisateur existe déjà en base
        $existingUser = $this->findOneBy(['username' => $ldapUserInfo['username']]);
        $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
        $roles = $ldapUserInfo['isManager']
            ? ['ROLE_MANAGER_' . $department]
            : ['ROLE_USER_' . $department];

        if ($existingUser) {
            // Mise à jour des informations LDAP
            $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
            $existingUser->setNom($nom ?? $existingUser->getNom());
            $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
            $existingUser->setRoles($roles);
            $existingUser->setDepartement($ldapUserInfo['department'] ?? $existingUser->getDepartement());
            $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
            $existingUser->setIsManager($ldapUserInfo['isManager']);
            $existingUser->setTitre($ldapUserInfo['title'] ?? null);
            $entityManager->flush();
            return $existingUser;
        }

        // Création du nouvel utilisateur
        $newUser = new User();
        // dd($ldapUserInfo);
        $newUser->setUsername($ldapUserInfo['username']);
        $newUser->setEmail($ldapUserInfo['email'] ?? null);
        $newUser->setNom($nom);
        $newUser->setPrenom($prenom);
        $newUser->setRoles($roles);
        $newUser->setRoles($ldapUserInfo['isManager'] ? ['ROLE_MANAGER'] : ['ROLE_USER']);
        $newUser->setDepartement($ldapUserInfo['department'] ?? null);
        $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
        $newUser->setIsManager($ldapUserInfo['isManager']);
        $newUser->setTitre($ldapUserInfo['title'] ?? null);
        $newUser->setPassword('LeMans72!');

        $entityManager->persist($newUser);
        $entityManager->flush();

        return $newUser;
    }

    private function normalizeDepartment(?string $department): string
    {
        if (empty($department)) {
            return 'DEFAULT';
        }
        // Remplace les espaces ou caractères spéciaux par des underscores
        $normalized = preg_replace('/[^A-Z0-9]/', '_', strtoupper($department));

        return $normalized;
    }

    public function explodeRole(array $roles): array
    {
        $newRoles = [];
        foreach ($roles as $role) {
            $newRoles[] = explode('_', $role);
        }
        return $newRoles;
    }
    /**
     * Trouve les utilisateurs par département
     */
    public function findByDepartment(string $department): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.departement = :department')
            ->setParameter('department', $department)
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les utilisateurs par rôle
     */
    public function findByRole(string $role): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.roles LIKE :role')
            ->setParameter('role', '%"' . $role . '"%')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les utilisateurs par département et rôle
     */
    public function findByDepartmentAndRole(string $department, string $role): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.departement = :department')
            ->andWhere('u.roles LIKE :role')
            ->setParameter('department', $department)
            ->setParameter('role', '%"' . $role . '"%')
            ->getQuery()
            ->getResult();
    }

    public function initializeUsers(): void
    {
        $entityManager = $this->getEntityManager();
        $allUsers = $this->ldapService->getAllUsersInfo();

        foreach ($allUsers as $ldapUserInfo) {
            // $serviceRepository = $entityManager->getRepository(Service::class);
            // $service = $serviceRepository->findOneBy(['name' => $ldapUserInfo['department']]);

            // if (!$service && isset($ldapUserInfo['department'])) {
            //     $service = new Service();
            //     $service->setName($ldapUserInfo['department']);
            //     $entityManager->persist($service);
            //     $entityManager->flush();
            // }

            $existingUser = $this->findOneBy(['username' => $ldapUserInfo['username']]);
            $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
            if ($nomComplet) {
                $parts = explode(' ', $matches[1], 2);
                $nom = $parts[0] ?? '';
                $prenom = $parts[1] ?? '';
            }
            //     if (preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches)) {
            //     $parts = explode(' ', $matches[1], 2);
            //     $nom = $parts[0] ?? null;
            //     $prenom = $parts[1] ?? null;
            // }

            $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
            $roles = $ldapUserInfo['isManager']
                ? ['ROLE_MANAGER_' . $department]
                : ['ROLE_USER_' . $department];
            // if($ldapUserInfo['department'] ==[]) {
                // dd($ldapUserInfo);

            if ($existingUser) {
                $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
                $existingUser->setNom($nom ?? $existingUser->getNom());
                $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
                $existingUser->setRoles($roles);
                $existingUser->setDepartement($ldapUserInfo['department'] ?? $existingUser->getDepartement());
                $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? $existingUser->getManager());
                $existingUser->setIsManager($ldapUserInfo['isManager'] ?? $existingUser->getIsManager());
                $existingUser->setTitre($ldapUserInfo['title'] ?? $existingUser->getTitre());
                $existingUser->setPassword('LeMans72!');
                $entityManager->flush();
            } elseif ($ldapUserInfo['email'] !== "<EMAIL>") {
                $newUser = new User();
                $newUser->setUsername($ldapUserInfo['username']);
                $newUser->setEmail($ldapUserInfo['email'] ?? null);
                $newUser->setNom($nom);
                $newUser->setPrenom($prenom);
                $newUser->setRoles($roles);
                $newUser->setDepartement($ldapUserInfo['department'] ?? '');
                $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
                $newUser->setIsManager($ldapUserInfo['isManager'] ?? null);
                $newUser->setTitre($ldapUserInfo['title'] ?? null);
                $newUser->setPassword('LeMans72!');
                $entityManager->persist($newUser);
                $entityManager->flush();
            }
        }
    }
}