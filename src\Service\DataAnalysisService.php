<?php

namespace App\Service;

use App\Entity\Document;
use App\Repository\DocumentRepository;
use Doctrine\ORM\EntityManagerInterface;

class DataAnalysisService
{
    private EntityManagerInterface $entityManager;
    private DocumentRepository $documentRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        DocumentRepository $documentRepository
    ) {
        $this->entityManager = $entityManager;
        $this->documentRepository = $documentRepository;
    }

    /**
     * Analyse les tendances des temps de traitement
     */
    public function analyzeProcessingTimeTrends(string $period = 'month', int $limit = 6): array
    {
        $trends = [];
        $now = new \DateTime();
        
        // Définir les périodes
        switch ($period) {
            case 'week':
                $interval = 'P1W'; // 1 semaine
                $format = 'W/Y';
                $label = 'Semaine ';
                break;
            case 'month':
                $interval = 'P1M'; // 1 mois
                $format = 'm/Y';
                $label = '';
                break;
            case 'quarter':
                $interval = 'P3M'; // 3 mois
                $format = 'Q/Y';
                $label = 'T';
                break;
            case 'year':
                $interval = 'P1Y'; // 1 an
                $format = 'Y';
                $label = '';
                break;
            default:
                $interval = 'P1M';
                $format = 'm/Y';
                $label = '';
        }
        
        // Générer les périodes
        $periods = [];
        $startDate = clone $now;
        
        for ($i = $limit - 1; $i >= 0; $i--) {
            $date = clone $startDate;
            
            // Soustraire l'intervalle i fois
            if ($i > 0) {
                switch ($period) {
                    case 'week':
                        $date->modify('-' . $i . ' weeks');
                        break;
                    case 'month':
                        $date->modify('-' . $i . ' months');
                        break;
                    case 'quarter':
                        $date->modify('-' . ($i * 3) . ' months');
                        break;
                    case 'year':
                        $date->modify('-' . $i . ' years');
                        break;
                }
            }
            
            $periodKey = $date->format($format);
            $periodLabel = $label . $date->format($format);
            
            $periods[$periodKey] = [
                'label' => $periodLabel,
                'start_date' => clone $date,
                'end_date' => clone $date,
                'avg_processing_time' => 0,
                'document_count' => 0,
                'total_time' => 0,
            ];
            
            // Ajuster la date de fin pour couvrir toute la période
            switch ($period) {
                case 'week':
                    $periods[$periodKey]['end_date']->modify('+6 days');
                    break;
                case 'month':
                    $periods[$periodKey]['end_date']->modify('last day of this month');
                    break;
                case 'quarter':
                    $periods[$periodKey]['end_date']->modify('+3 months -1 day');
                    break;
                case 'year':
                    $periods[$periodKey]['end_date']->modify('last day of december this year');
                    break;
            }
        }
        
        // Récupérer tous les documents
        $documents = $this->documentRepository->findAll();
        
        foreach ($documents as $document) {
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }
            
            // Trouver la première et la dernière date
            $firstDate = null;
            $lastDate = null;
            
            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['enter'])) {
                            $enterDate = new \DateTime($entry['enter']);
                            if ($firstDate === null || $enterDate < $firstDate) {
                                $firstDate = $enterDate;
                            }
                        }
                        
                        if (isset($entry['exit']) && $entry['exit'] !== null) {
                            $exitDate = new \DateTime($entry['exit']);
                            if ($lastDate === null || $exitDate > $lastDate) {
                                $lastDate = $exitDate;
                            }
                        }
                    }
                } elseif (is_string($entries)) {
                    // Ancien format
                    $date = new \DateTime($entries);
                    if ($firstDate === null || $date < $firstDate) {
                        $firstDate = $date;
                    }
                    if ($lastDate === null || $date > $lastDate) {
                        $lastDate = $date;
                    }
                }
            }
            
            if ($firstDate && $lastDate) {
                $diff = $lastDate->diff($firstDate);
                $processingTime = $diff->days;
                
                // Attribuer à la période correspondante
                foreach ($periods as $periodKey => &$periodData) {
                    if ($firstDate >= $periodData['start_date'] && $firstDate <= $periodData['end_date']) {
                        $periodData['document_count']++;
                        $periodData['total_time'] += $processingTime;
                        break;
                    }
                }
            }
        }
        
        // Calculer les moyennes
        foreach ($periods as $periodKey => &$periodData) {
            if ($periodData['document_count'] > 0) {
                $periodData['avg_processing_time'] = round($periodData['total_time'] / $periodData['document_count'], 1);
            }
        }
        
        return $periods;
    }
    
    /**
     * Prédit le temps de traitement pour un document
     */
    public function predictProcessingTime(Document $document): array
    {
        $docType = $document->getDocType();
        $procType = $document->getProcType();
        $materialType = $document->getMaterialType();
        
        // Récupérer des documents similaires
        $similarDocuments = $this->documentRepository->findSimilarDocuments($docType, $procType, $materialType);
        
        $totalTime = 0;
        $count = 0;
        $minTime = PHP_INT_MAX;
        $maxTime = 0;
        
        foreach ($similarDocuments as $similarDoc) {
            $timestamps = $similarDoc->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }
            
            // Calculer le temps total de traitement
            $firstDate = null;
            $lastDate = null;
            
            foreach ($timestamps as $state => $entries) {
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        if (isset($entry['enter'])) {
                            $enterDate = new \DateTime($entry['enter']);
                            if ($firstDate === null || $enterDate < $firstDate) {
                                $firstDate = $enterDate;
                            }
                        }
                        
                        if (isset($entry['exit']) && $entry['exit'] !== null) {
                            $exitDate = new \DateTime($entry['exit']);
                            if ($lastDate === null || $exitDate > $lastDate) {
                                $lastDate = $exitDate;
                            }
                        }
                    }
                } elseif (is_string($entries)) {
                    // Ancien format
                    $date = new \DateTime($entries);
                    if ($firstDate === null || $date < $firstDate) {
                        $firstDate = $date;
                    }
                    if ($lastDate === null || $date > $lastDate) {
                        $lastDate = $date;
                    }
                }
            }
            
            if ($firstDate && $lastDate) {
                $diff = $lastDate->diff($firstDate);
                $processingTime = $diff->days;
                $totalTime += $processingTime;
                $count++;
                
                $minTime = min($minTime, $processingTime);
                $maxTime = max($maxTime, $processingTime);
            }
        }
        
        $avgTime = $count > 0 ? round($totalTime / $count, 1) : null;
        
        return [
            'avg_time' => $avgTime,
            'min_time' => $minTime < PHP_INT_MAX ? $minTime : null,
            'max_time' => $maxTime > 0 ? $maxTime : null,
            'sample_size' => $count,
        ];
    }
    
    /**
     * Identifie les documents à risque (stagnants)
     */
    public function identifyRiskyDocuments(int $thresholdDays = 7): array
    {
        $riskyDocuments = [];
        $now = new \DateTime();
        
        $documents = $this->documentRepository->findAll();
        
        foreach ($documents as $document) {
            $currentSteps = $document->getCurrentSteps();
            if (!$currentSteps) {
                continue;
            }
            
            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }
            
            foreach ($currentSteps as $state => $value) {
                if (!isset($timestamps[$state])) {
                    continue;
                }
                
                $entries = $timestamps[$state];
                $lastEntry = null;
                
                if (is_array($entries)) {
                    $lastEntry = end($entries);
                    if (isset($lastEntry['enter'])) {
                        $enterDate = new \DateTime($lastEntry['enter']);
                    } else {
                        continue;
                    }
                } elseif (is_string($entries)) {
                    $enterDate = new \DateTime($entries);
                } else {
                    continue;
                }
                
                $diff = $now->diff($enterDate);
                $daysSinceEnter = $diff->days;
                
                if ($daysSinceEnter >= $thresholdDays) {
                    $riskyDocuments[] = [
                        'document' => $document,
                        'state' => $state,
                        'days_in_state' => $daysSinceEnter,
                        'enter_date' => $enterDate,
                    ];
                }
            }
        }
        
        // Trier par nombre de jours décroissant
        usort($riskyDocuments, function($a, $b) {
            return $b['days_in_state'] - $a['days_in_state'];
        });
        
        return $riskyDocuments;
    }
}
