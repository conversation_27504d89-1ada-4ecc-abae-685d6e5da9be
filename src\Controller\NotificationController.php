<?php

namespace App\Controller;

use App\Repository\NotificationRepository;
use App\Service\NotificationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/notifications')]
class NotificationController extends AbstractController
{
    private NotificationRepository $notificationRepository;
    private NotificationService $notificationService;

    public function __construct(
        NotificationRepository $notificationRepository,
        NotificationService $notificationService
    ) {
        $this->notificationRepository = $notificationRepository;
        $this->notificationService = $notificationService;
    }

    #[Route('/', name: 'app_notifications', methods: ['GET'])]
    public function index(): Response
    {
        $user = $this->getUser();
        
        // Récupérer toutes les notifications de l'utilisateur
        $notifications = $this->notificationRepository->findAllByUser($user);
        
        // Compter les notifications non lues
        $unreadCount = $this->notificationRepository->countUnreadByUser($user);
        
        return $this->render('notification/index.html.twig', [
            'notifications' => $notifications,
            'unread_count' => $unreadCount,
        ]);
    }
    
    #[Route('/mark-as-read/{id}', name: 'app_notifications_mark_as_read', methods: ['POST'])]
    public function markAsRead(int $id): JsonResponse
    {
        $user = $this->getUser();
        $notification = $this->notificationRepository->find($id);
        
        if (!$notification) {
            return new JsonResponse(['success' => false, 'message' => 'Notification non trouvée'], 404);
        }
        
        if ($notification->getUser() !== $user) {
            return new JsonResponse(['success' => false, 'message' => 'Vous n\'êtes pas autorisé à marquer cette notification comme lue'], 403);
        }
        
        $this->notificationService->markAsRead($notification);
        
        return new JsonResponse(['success' => true]);
    }
    
    #[Route('/mark-all-as-read', name: 'app_notifications_mark_all_as_read', methods: ['POST'])]
    public function markAllAsRead(): JsonResponse
    {
        $user = $this->getUser();
        
        $this->notificationService->markAllAsRead($user);
        
        return new JsonResponse(['success' => true]);
    }
    
    #[Route('/count', name: 'app_notifications_count', methods: ['GET'])]
    public function count(): JsonResponse
    {
        $user = $this->getUser();
        
        $count = $this->notificationRepository->countUnreadByUser($user);
        
        return new JsonResponse(['count' => $count]);
    }
    
    #[Route('/latest', name: 'app_notifications_latest', methods: ['GET'])]
    public function latest(): JsonResponse
    {
        $user = $this->getUser();
        
        $notifications = $this->notificationRepository->findUnreadByUser($user, 5);
        
        $formattedNotifications = [];
        foreach ($notifications as $notification) {
            $formattedNotifications[] = [
                'id' => $notification->getId(),
                'title' => $notification->getTitle(),
                'message' => $notification->getMessage(),
                'type' => $notification->getType(),
                'severity' => $notification->getSeverity(),
                'created_at' => $notification->getCreatedAt()->format('Y-m-d H:i:s'),
                'link' => $notification->getLink(),
            ];
        }
        
        return new JsonResponse([
            'notifications' => $formattedNotifications,
            'count' => count($formattedNotifications),
        ]);
    }
    
    #[Route('/generate-test-notifications', name: 'app_notifications_generate_test', methods: ['GET'])]
    public function generateTestNotifications(): Response
    {
        $user = $this->getUser();
        
        // Créer quelques notifications de test
        $this->notificationService->createNotification(
            $user,
            'document_stagnant',
            'Document stagnant',
            'Le document REF0001 est dans l\'état "BE" depuis 10 jours.',
            null,
            '/document/1',
            'warning'
        );
        
        $this->notificationService->createNotification(
            $user,
            'document_returned',
            'Document retourné',
            'Le document REF0002 a été retourné à l\'état "BE_1".',
            null,
            '/document/2',
            'danger'
        );
        
        $this->notificationService->createNotification(
            $user,
            'document_validated',
            'Document validé',
            'Le document REF0003 a été validé avec succès.',
            null,
            '/document/3',
            'success'
        );
        
        $this->notificationService->createNotification(
            $user,
            'system',
            'Maintenance prévue',
            'Une maintenance du système est prévue ce soir à 22h00.',
            null,
            null,
            'info'
        );
        
        return $this->redirectToRoute('app_notifications');
    }
}
