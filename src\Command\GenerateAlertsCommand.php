<?php

namespace App\Command;

use App\Service\DataAnalysisService;
use App\Service\NotificationService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:generate-alerts',
    description: 'Génère des alertes pour les documents à risque',
)]
class GenerateAlertsCommand extends Command
{
    private DataAnalysisService $dataAnalysisService;
    private NotificationService $notificationService;

    public function __construct(
        DataAnalysisService $dataAnalysisService,
        NotificationService $notificationService
    ) {
        parent::__construct();
        $this->dataAnalysisService = $dataAnalysisService;
        $this->notificationService = $notificationService;
    }

    protected function configure(): void
    {
        $this
            ->addOption('threshold', null, InputOption::VALUE_REQUIRED, 'Seuil en jours pour considérer un document comme à risque', 7)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $threshold = $input->getOption('threshold');
        
        $io->title('Génération des alertes pour les documents à risque');
        
        // Identifier les documents à risque
        $riskyDocuments = $this->dataAnalysisService->identifyRiskyDocuments($threshold);
        
        $io->info(sprintf('Nombre de documents à risque trouvés : %d', count($riskyDocuments)));
        
        // Générer des alertes pour chaque document à risque
        $alertCount = 0;
        foreach ($riskyDocuments as $riskyDocument) {
            $document = $riskyDocument['document'];
            $state = $riskyDocument['state'];
            $daysInState = $riskyDocument['days_in_state'];
            
            // Créer une notification pour le superviseur du document
            $this->notificationService->createStagnantDocumentNotification($document, $daysInState, $state);
            
            $alertCount++;
            
            $io->text(sprintf(
                'Alerte créée pour le document %s (Ref: %s) - %d jours dans l\'état "%s"',
                $document->getRefTitleFra() ?? 'sans titre',
                $document->getReference(),
                $daysInState,
                $state
            ));
        }
        
        $io->success(sprintf('%d alertes ont été générées avec succès.', $alertCount));
        
        return Command::SUCCESS;
    }
}
