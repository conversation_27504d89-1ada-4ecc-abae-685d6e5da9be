/**
 * Gestionnaire de notifications
 */
class NotificationManager {
    constructor(options = {}) {
        this.options = Object.assign({
            countUrl: '/notifications/count',
            latestUrl: '/notifications/latest',
            markAsReadUrl: '/notifications/mark-as-read/',
            checkInterval: 60000, // 1 minute
            notificationContainer: '#notificationDropdown',
            notificationBadge: '.notification-badge',
            notificationList: '.dropdown-menu[aria-labelledby="notificationDropdown"]',
            notificationTemplate: `
                <li>
                    <a class="dropdown-item" href="{link}">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-{icon} text-{severity} me-2"></i>
                            <div>
                                <div class="fw-bold">{title}</div>
                                <div class="small text-muted">{message}</div>
                                <div class="small text-muted">{time}</div>
                            </div>
                        </div>
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
            `
        }, options);

        this.lastCount = 0;
        this.checkInterval = null;

        this.init();
    }

    /**
     * Initialise le gestionnaire de notifications
     */
    init() {
        // Vérifier les notifications au chargement de la page
        this.checkNotifications();

        // Configurer la vérification périodique
        this.checkInterval = setInterval(() => {
            this.checkNotifications();
        }, this.options.checkInterval);

        // Ajouter les écouteurs d'événements
        document.addEventListener('click', (event) => {
            if (event.target.matches('.mark-notification-read')) {
                this.markAsRead(event.target.dataset.id);
            }
        });
    }

    /**
     * Vérifie les nouvelles notifications
     */
    checkNotifications() {
        fetch(this.options.countUrl)
            .then(response => response.json())
            .then(data => {
                const count = data.count;

                // Si le nombre de notifications a changé, mettre à jour l'interface
                if (count !== this.lastCount) {
                    this.lastCount = count;
                    this.updateBadge(count);

                    // Si de nouvelles notifications sont arrivées, les récupérer
                    if (count > 0) {
                        this.fetchLatestNotifications();
                    }
                }
            })
            .catch(error => {
                console.error('Erreur lors de la vérification des notifications:', error);
            });
    }

    /**
     * Récupère les dernières notifications
     */
    fetchLatestNotifications() {
        fetch(this.options.latestUrl)
            .then(response => response.json())
            .then(data => {
                this.updateNotificationList(data.notifications);
            })
            .catch(error => {
                console.error('Erreur lors de la récupération des notifications:', error);
            });
    }

    /**
     * Met à jour le badge de notification
     */
    updateBadge(count) {
        // Mettre à jour le badge dans le menu déroulant
        const container = document.querySelector(this.options.notificationContainer);
        if (container) {
            let badge = container.querySelector(this.options.notificationBadge);

            if (count > 0) {
                if (!badge) {
                    badge = document.createElement('span');
                    badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge';
                    container.appendChild(badge);
                }
                badge.textContent = count;
            } else if (badge) {
                badge.remove();
            }
        }

        // Mettre à jour le badge dans la barre de navigation
        const navBadge = document.getElementById('notificationBadge');
        if (navBadge) {
            if (count > 0) {
                navBadge.textContent = count;
                navBadge.style.display = 'block';
            } else {
                navBadge.style.display = 'none';
            }
        }
    }

    /**
     * Met à jour la liste des notifications
     */
    updateNotificationList(notifications) {
        const container = document.querySelector(this.options.notificationContainer);
        if (!container) return;

        const list = container.querySelector(this.options.notificationList);
        if (!list) return;

        // Vider la liste
        list.innerHTML = '';

        // Ajouter les nouvelles notifications
        if (notifications.length > 0) {
            notifications.forEach(notification => {
                const html = this.renderNotification(notification);
                list.insertAdjacentHTML('beforeend', html);
            });
        } else {
            list.insertAdjacentHTML('beforeend', '<li><span class="dropdown-item">Aucune notification non lue</span></li>');
        }

        // Ajouter le lien pour voir toutes les notifications
        list.insertAdjacentHTML('beforeend', `
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-center" href="/notifications">Voir toutes les notifications</a></li>
        `);
    }

    /**
     * Rend une notification en HTML
     */
    renderNotification(notification) {
        let icon = 'info-circle';

        switch (notification.severity) {
            case 'danger':
                icon = 'exclamation-circle';
                break;
            case 'warning':
                icon = 'exclamation-triangle';
                break;
            case 'success':
                icon = 'check-circle';
                break;
        }

        const link = notification.link || '/notifications';
        const message = notification.message.length > 50 ? notification.message.substring(0, 50) + '...' : notification.message;

        return this.options.notificationTemplate
            .replace('{link}', link)
            .replace('{icon}', icon)
            .replace('{severity}', notification.severity)
            .replace('{title}', notification.title)
            .replace('{message}', message)
            .replace('{time}', this.formatDate(notification.created_at));
    }

    /**
     * Formate une date
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    /**
     * Marque une notification comme lue
     */
    markAsRead(id) {
        fetch(this.options.markAsReadUrl + id, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.checkNotifications();
            }
        })
        .catch(error => {
            console.error('Erreur lors du marquage de la notification comme lue:', error);
        });
    }

    /**
     * Arrête la vérification périodique
     */
    stop() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }
}

// Initialiser le gestionnaire de notifications
document.addEventListener('DOMContentLoaded', function() {
    window.notificationManager = new NotificationManager();
});
